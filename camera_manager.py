#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Camera management module for SmartPSS-like monitoring system
Handles RTSP streaming, camera connections, and ONVIF operations
"""

import cv2
import threading
import time
import requests
from datetime import datetime
from PyQt5.QtCore import QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QImage, QPixmap
import numpy as np
from database import DatabaseManager

class CameraStream(QThread):
    """Thread for handling camera RTSP stream"""
    frame_ready = pyqtSignal(np.ndarray, int)  # frame, camera_id
    connection_lost = pyqtSignal(int)  # camera_id
    connection_restored = pyqtSignal(int)  # camera_id
    
    def __init__(self, camera_id, rtsp_url):
        super().__init__()
        self.camera_id = camera_id
        self.rtsp_url = rtsp_url
        self.running = False
        self.cap = None
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.connection_status = False
        
    def run(self):
        """Main streaming loop"""
        self.running = True
        
        while self.running:
            try:
                if not self.cap or not self.cap.isOpened():
                    self.connect_camera()
                
                if self.cap and self.cap.isOpened():
                    ret, frame = self.cap.read()
                    if ret:
                        self.frame_ready.emit(frame, self.camera_id)
                        self.reconnect_attempts = 0
                        
                        if not self.connection_status:
                            self.connection_status = True
                            self.connection_restored.emit(self.camera_id)
                    else:
                        self.handle_connection_loss()
                else:
                    self.handle_connection_loss()
                    
            except Exception as e:
                print(f"Camera {self.camera_id} error: {e}")
                self.handle_connection_loss()
            
            time.sleep(0.033)  # ~30 FPS
    
    def connect_camera(self):
        """Connect to camera RTSP stream with timeout"""
        try:
            self.cap = cv2.VideoCapture(self.rtsp_url)
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            self.cap.set(cv2.CAP_PROP_FPS, 25)

            # Set timeout properties to prevent hanging
            self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 5000)  # 5 seconds timeout
            self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 3000)  # 3 seconds read timeout

            # Test connection with timeout
            if self.cap.isOpened():
                ret, _ = self.cap.read()
                if not ret:
                    raise Exception("Failed to read from camera")
            else:
                raise Exception("Failed to open camera stream")

        except Exception as e:
            print(f"Failed to connect to camera {self.camera_id}: {e}")
            if self.cap:
                self.cap.release()
                self.cap = None
    
    def handle_connection_loss(self):
        """Handle camera connection loss"""
        if self.connection_status:
            self.connection_status = False
            self.connection_lost.emit(self.camera_id)

        # Clean up current connection
        if self.cap:
            self.cap.release()
            self.cap = None

        self.reconnect_attempts += 1
        if self.reconnect_attempts < self.max_reconnect_attempts:
            time.sleep(1)  # Shorter wait before reconnecting
        else:
            time.sleep(5)  # Shorter wait after multiple failures
            self.reconnect_attempts = 0
    
    def stop(self):
        """Stop streaming"""
        self.running = False
        if self.cap:
            self.cap.release()
        self.wait()

class CameraManager:
    """Main camera management class"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.active_streams = {}
        self.active_cameras = {}  # إضافة المتغير المفقود
        self.camera_widgets = {}

        # إعداد نظام السجلات
        import logging
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
        
    def get_cameras(self):
        """Get all active cameras from database"""
        return self.db.get_cameras(active_only=True)
    
    def add_camera(self, camera_data):
        """Add new camera to database"""
        try:
            # التحقق من الاتصال بالكاميرا
            camera_url = camera_data.get('rtsp_url', '')
            connection_success = False

            if camera_url:
                connection_success = self.test_camera_connection(camera_url)
                self.logger.info(f"Connection test for {camera_data.get('name', 'Unknown')}: {'Success' if connection_success else 'Failed'}")

            # إضافة الكاميرا لقاعدة البيانات
            camera_id = self.db.add_camera(camera_data)

            if camera_id:
                # إضافة الكاميرا للقائمة النشطة
                self.active_cameras[camera_id] = {
                    'config': camera_data,
                    'stream': None,
                    'last_frame': None,
                    'connected': connection_success
                }

                if connection_success:
                    self.logger.info(f"Camera added and connected successfully: {camera_data.get('name', 'Unknown')}")
                else:
                    self.logger.warning(f"Camera added but not connected: {camera_data.get('name', 'Unknown')}")

                return camera_id
            else:
                raise Exception("فشل في حفظ الكاميرا في قاعدة البيانات")

        except Exception as e:
            self.logger.error(f"Failed to add camera: {e}")
            raise Exception(f"فشل في إضافة الكاميرا: {str(e)}")

    def get_cameras(self):
        """Get all cameras"""
        try:
            return self.db.get_cameras()
        except Exception as e:
            self.logger.error(f"Failed to get cameras: {e}")
            return []

    def remove_camera(self, camera_id):
        """Remove camera completely"""
        try:
            # إيقاف البث إذا كان نشطاً
            if camera_id in self.active_cameras:
                self.stop_stream(camera_id)
                del self.active_cameras[camera_id]

            # إيقاف البث من القائمة النشطة أيضاً
            if camera_id in self.active_streams:
                self.stop_stream(camera_id)

            # حذف من قاعدة البيانات نهائياً
            success = self.db.remove_camera(camera_id)

            if success:
                self.logger.info(f"Camera {camera_id} removed successfully from database and streams")
                print(f"✅ تم حذف الكاميرا {camera_id} نهائياً من النظام")
            else:
                self.logger.warning(f"Failed to remove camera {camera_id} from database")
                print(f"❌ فشل في حذف الكاميرا {camera_id} من قاعدة البيانات")

            return success

        except Exception as e:
            self.logger.error(f"Failed to remove camera {camera_id}: {e}")
            print(f"❌ خطأ في حذف الكاميرا {camera_id}: {e}")
            return False
    
    def update_camera(self, camera_id, camera_data):
        """Update camera information"""
        try:
            # التحقق من الاتصال بالكاميرا الجديدة
            camera_url = camera_data.get('rtsp_url', '')
            connection_success = False

            if camera_url:
                connection_success = self.test_camera_connection(camera_url)
                self.logger.info(f"Connection test for updated camera {camera_data.get('name', 'Unknown')}: {'Success' if connection_success else 'Failed'}")

            # تحديث الكاميرا في قاعدة البيانات
            success = self.db.update_camera(camera_id, camera_data)

            if success:
                # تحديث الكاميرا في القائمة النشطة
                if camera_id in self.active_cameras:
                    # إيقاف البث القديم
                    if self.active_cameras[camera_id].get('stream'):
                        self.stop_stream(camera_id)

                    # تحديث البيانات
                    self.active_cameras[camera_id] = {
                        'config': camera_data,
                        'stream': None,
                        'last_frame': None,
                        'connected': connection_success
                    }
                else:
                    # إضافة الكاميرا للقائمة النشطة إذا لم تكن موجودة
                    self.active_cameras[camera_id] = {
                        'config': camera_data,
                        'stream': None,
                        'last_frame': None,
                        'connected': connection_success
                    }

                if connection_success:
                    self.logger.info(f"Camera updated and connected successfully: {camera_data.get('name', 'Unknown')}")
                else:
                    self.logger.warning(f"Camera updated but not connected: {camera_data.get('name', 'Unknown')}")

                return True
            else:
                raise Exception("فشل في تحديث الكاميرا في قاعدة البيانات")

        except Exception as e:
            self.logger.error(f"Failed to update camera {camera_id}: {e}")
            raise Exception(f"فشل في تحديث الكاميرا: {str(e)}")
    
    def delete_camera(self, camera_id):
        """Delete camera"""
        # Stop stream if active
        if camera_id in self.active_streams:
            self.stop_stream(camera_id)
        
        self.db.delete_camera(camera_id)
    
    def test_camera_connection(self, rtsp_url, timeout=5):
        """Test camera RTSP connection with timeout"""
        cap = None
        try:
            cap = cv2.VideoCapture(rtsp_url)
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

            # Set timeout properties to prevent hanging
            cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, timeout * 1000)  # Convert to milliseconds
            cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 3000)  # 3 seconds read timeout

            # Check if camera opened successfully
            if not cap.isOpened():
                return False

            # Try to read a frame with timeout
            ret, frame = cap.read()

            return ret and frame is not None

        except Exception as e:
            print(f"Connection test failed: {e}")
            return False
        finally:
            if cap:
                cap.release()
    
    def start_stream(self, camera_id, rtsp_url):
        """Start camera stream"""
        if camera_id not in self.active_streams:
            stream = CameraStream(camera_id, rtsp_url)
            self.active_streams[camera_id] = stream
            stream.start()
            return stream
        return self.active_streams[camera_id]
    
    def stop_stream(self, camera_id):
        """Stop camera stream"""
        if camera_id in self.active_streams:
            self.active_streams[camera_id].stop()
            del self.active_streams[camera_id]
    
    def stop_all_streams(self):
        """Stop all active streams"""
        for camera_id in list(self.active_streams.keys()):
            self.stop_stream(camera_id)
    
    def get_stream(self, camera_id):
        """Get active stream for camera"""
        return self.active_streams.get(camera_id)
    
    def build_rtsp_url(self, ip_address, port, username, password, path="/cam/realmonitor"):
        """Build RTSP URL from camera parameters"""
        if username and password:
            return f"rtsp://{username}:{password}@{ip_address}:{port}{path}"
        else:
            return f"rtsp://{ip_address}:{port}{path}"
    
    def discover_onvif_cameras(self, network_range="***********/24"):
        """Discover ONVIF cameras on network (basic implementation)"""
        discovered_cameras = []
        
        try:
            # Basic network scan for common camera ports
            import socket
            import ipaddress
            
            network = ipaddress.IPv4Network(network_range, strict=False)
            common_ports = [554, 8554, 80, 8080]
            
            for ip in network.hosts():
                ip_str = str(ip)
                for port in common_ports:
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(1)
                        result = sock.connect_ex((ip_str, port))
                        sock.close()
                        
                        if result == 0:
                            # Try to get camera info
                            camera_info = self.probe_camera_info(ip_str, port)
                            if camera_info:
                                discovered_cameras.append(camera_info)
                                break
                                
                    except Exception:
                        continue
                        
        except Exception as e:
            print(f"Discovery error: {e}")
        
        return discovered_cameras
    
    def probe_camera_info(self, ip_address, port):
        """Probe camera for basic information"""
        try:
            # Try common RTSP paths
            common_paths = [
                "/cam/realmonitor",
                "/live",
                "/stream1",
                "/video1",
                "/h264",
                "/mpeg4"
            ]
            
            for path in common_paths:
                rtsp_url = f"rtsp://{ip_address}:{port}{path}"
                if self.test_camera_connection(rtsp_url):
                    return {
                        'ip_address': ip_address,
                        'port': port,
                        'rtsp_path': path,
                        'rtsp_url': rtsp_url,
                        'name': f"Camera_{ip_address}_{port}"
                    }
                    
        except Exception:
            pass
        
        return None
    
    def get_camera_snapshot(self, camera_id):
        """Get current frame snapshot from camera"""
        if camera_id in self.active_streams:
            stream = self.active_streams[camera_id]
            if stream.cap and stream.cap.isOpened():
                ret, frame = stream.cap.read()
                if ret:
                    return frame
        return None
    
    def frame_to_qimage(self, frame):
        """Convert OpenCV frame to QImage"""
        if frame is None:
            return None
            
        height, width, channel = frame.shape
        bytes_per_line = 3 * width
        
        # Convert BGR to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        q_image = QImage(rgb_frame.data, width, height, bytes_per_line, QImage.Format_RGB888)
        return q_image
    
    def frame_to_pixmap(self, frame, width=None, height=None):
        """Convert OpenCV frame to QPixmap"""
        q_image = self.frame_to_qimage(frame)
        if q_image is None:
            return None
            
        pixmap = QPixmap.fromImage(q_image)
        
        if width and height:
            pixmap = pixmap.scaled(width, height)
            
        return pixmap
