#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                           QLineEdit, QPushButton, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

class SimpleLoginDialog(QDialog):
    """نافذة تسجيل دخول بسيطة جداً"""
    
    login_successful = pyqtSignal(dict)
    
    def __init__(self, user_manager, parent=None):
        super().__init__(parent)
        self.user_manager = user_manager
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة بسيطة"""
        self.setWindowTitle("تسجيل الدخول")
        self.setFixedSize(300, 200)
        self.setModal(True)
        
        # تخطيط رئيسي
        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان
        title = QLabel("تسجيل الدخول")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(title)
        
        # اسم المستخدم
        layout.addWidget(QLabel("اسم المستخدم:"))
        self.username_edit = QLineEdit()
        self.username_edit.setText("admin")
        self.username_edit.setMinimumHeight(30)
        layout.addWidget(self.username_edit)
        
        # كلمة المرور
        layout.addWidget(QLabel("كلمة المرور:"))
        self.password_edit = QLineEdit()
        self.password_edit.setText("admin")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setMinimumHeight(30)
        layout.addWidget(self.password_edit)
        
        # تذكرني
        self.remember_checkbox = QCheckBox("تذكرني")
        self.remember_checkbox.setChecked(True)
        layout.addWidget(self.remember_checkbox)
        
        # أزرار
        button_layout = QHBoxLayout()
        
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        login_btn = QPushButton("دخول")
        login_btn.setDefault(True)
        login_btn.clicked.connect(self.attempt_login)
        button_layout.addWidget(login_btn)
        
        layout.addLayout(button_layout)
        
        # رسالة الحالة
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
        
        # ربط Enter بتسجيل الدخول
        self.username_edit.returnPressed.connect(self.attempt_login)
        self.password_edit.returnPressed.connect(self.attempt_login)
        
        # تركيز على اسم المستخدم
        self.username_edit.setFocus()
        
    def attempt_login(self):
        """محاولة تسجيل الدخول"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        if not username:
            self.status_label.setText("أدخل اسم المستخدم")
            self.status_label.setStyleSheet("color: red;")
            return
            
        if not password:
            self.status_label.setText("أدخل كلمة المرور")
            self.status_label.setStyleSheet("color: red;")
            return
        
        # محاولة المصادقة
        success, message = self.user_manager.authenticate(username, password)
        
        if success:
            self.status_label.setText("تم تسجيل الدخول بنجاح")
            self.status_label.setStyleSheet("color: green;")
            
            user_info = self.user_manager.get_current_user()
            self.login_successful.emit(user_info)
            self.accept()
        else:
            self.status_label.setText(f"خطأ: {message}")
            self.status_label.setStyleSheet("color: red;")
            self.password_edit.clear()
            self.password_edit.setFocus()
