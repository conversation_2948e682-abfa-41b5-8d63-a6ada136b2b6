#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Video recording module for SmartPSS-like monitoring system
Handles manual and automatic video recording with scheduling
"""

import cv2
import os
import threading
import time
from datetime import datetime, timedelta
import schedule
from PyQt5.QtCore import QThread, pyqtSignal, QTimer
from database import DatabaseManager

class VideoRecorder(QThread):
    """Video recording thread for a single camera"""
    
    recording_started = pyqtSignal(int, str)  # camera_id, filename
    recording_stopped = pyqtSignal(int, str, int)  # camera_id, filename, duration
    recording_error = pyqtSignal(int, str)  # camera_id, error_message
    
    def __init__(self, camera_id, output_path, fps=30, quality="high"):
        super().__init__()
        self.camera_id = camera_id
        self.output_path = output_path
        self.fps = fps
        self.quality = quality
        self.recording = False
        self.current_frame = None
        self.video_writer = None
        self.start_time = None
        self.frame_count = 0
        
        # Quality settings
        self.quality_settings = {
            "low": {"width": 640, "height": 480, "bitrate": 500000},
            "medium": {"width": 1280, "height": 720, "bitrate": 1500000},
            "high": {"width": 1920, "height": 1080, "bitrate": 3000000}
        }
    
    def set_frame(self, frame):
        """Update current frame for recording"""
        self.current_frame = frame.copy() if frame is not None else None
    
    def start_recording(self):
        """Start video recording"""
        if not self.recording:
            self.recording = True
            self.start_time = datetime.now()
            self.frame_count = 0
            self.start()
    
    def stop_recording(self):
        """Stop video recording"""
        self.recording = False
    
    def run(self):
        """Main recording loop"""
        try:
            # Initialize video writer
            if not self.init_video_writer():
                self.recording_error.emit(self.camera_id, "Failed to initialize video writer")
                return
            
            self.recording_started.emit(self.camera_id, os.path.basename(self.output_path))
            
            while self.recording and self.current_frame is not None:
                try:
                    # Resize frame if needed
                    frame = self.prepare_frame(self.current_frame)
                    
                    if frame is not None:
                        self.video_writer.write(frame)
                        self.frame_count += 1
                    
                    time.sleep(1.0 / self.fps)  # Control frame rate
                    
                except Exception as e:
                    print(f"Recording frame error: {e}")
                    continue
            
            # Finalize recording
            self.finalize_recording()
            
        except Exception as e:
            self.recording_error.emit(self.camera_id, str(e))
        finally:
            self.cleanup()
    
    def init_video_writer(self):
        """Initialize OpenCV video writer"""
        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(self.output_path), exist_ok=True)
            
            # Get quality settings
            settings = self.quality_settings.get(self.quality, self.quality_settings["medium"])
            
            # Define codec and create VideoWriter
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            self.video_writer = cv2.VideoWriter(
                self.output_path,
                fourcc,
                self.fps,
                (settings["width"], settings["height"])
            )
            
            return self.video_writer.isOpened()
            
        except Exception as e:
            print(f"Video writer initialization error: {e}")
            return False
    
    def prepare_frame(self, frame):
        """Prepare frame for recording (resize, format)"""
        if frame is None:
            return None
        
        try:
            settings = self.quality_settings.get(self.quality, self.quality_settings["medium"])
            target_width = settings["width"]
            target_height = settings["height"]
            
            # Resize frame
            resized_frame = cv2.resize(frame, (target_width, target_height))
            
            return resized_frame
            
        except Exception as e:
            print(f"Frame preparation error: {e}")
            return None
    
    def finalize_recording(self):
        """Finalize recording and save metadata"""
        if self.start_time:
            duration = int((datetime.now() - self.start_time).total_seconds())
            self.recording_stopped.emit(self.camera_id, os.path.basename(self.output_path), duration)
    
    def cleanup(self):
        """Clean up resources"""
        if self.video_writer:
            self.video_writer.release()
            self.video_writer = None

class RecordingManager:
    """Manages video recording for multiple cameras"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.active_recordings = {}
        self.scheduled_recordings = {}
        self.recordings_dir = "videos"
        
        # Ensure recordings directory exists
        os.makedirs(self.recordings_dir, exist_ok=True)
        
        # Initialize scheduler
        self.scheduler_thread = threading.Thread(target=self.run_scheduler, daemon=True)
        self.scheduler_running = True
        self.scheduler_thread.start()
    
    def start_manual_recording(self, camera_id, duration_minutes=None):
        """Start manual recording for a camera"""
        if camera_id in self.active_recordings:
            return False, "Recording already active for this camera"
        
        try:
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"manual_cam{camera_id}_{timestamp}.mp4"
            output_path = os.path.join(self.recordings_dir, filename)
            
            # Create recorder
            recorder = VideoRecorder(camera_id, output_path)
            recorder.recording_started.connect(self.on_recording_started)
            recorder.recording_stopped.connect(self.on_recording_stopped)
            recorder.recording_error.connect(self.on_recording_error)
            
            self.active_recordings[camera_id] = {
                'recorder': recorder,
                'type': 'manual',
                'start_time': datetime.now(),
                'duration_minutes': duration_minutes,
                'filename': filename,
                'output_path': output_path
            }
            
            recorder.start_recording()
            
            # Set timer for automatic stop if duration specified
            if duration_minutes:
                timer = QTimer()
                timer.timeout.connect(lambda: self.stop_recording(camera_id))
                timer.start(duration_minutes * 60 * 1000)  # Convert to milliseconds
                self.active_recordings[camera_id]['timer'] = timer
            
            return True, "Recording started successfully"
            
        except Exception as e:
            return False, f"Failed to start recording: {e}"
    
    def start_motion_recording(self, camera_id):
        """Start motion-triggered recording"""
        if camera_id in self.active_recordings:
            return False, "Recording already active for this camera"
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"motion_cam{camera_id}_{timestamp}.mp4"
            output_path = os.path.join(self.recordings_dir, filename)
            
            recorder = VideoRecorder(camera_id, output_path)
            recorder.recording_started.connect(self.on_recording_started)
            recorder.recording_stopped.connect(self.on_recording_stopped)
            recorder.recording_error.connect(self.on_recording_error)
            
            self.active_recordings[camera_id] = {
                'recorder': recorder,
                'type': 'motion',
                'start_time': datetime.now(),
                'filename': filename,
                'output_path': output_path,
                'motion_triggered': True
            }
            
            recorder.start_recording()
            
            # Auto-stop after 5 minutes for motion recordings
            timer = QTimer()
            timer.timeout.connect(lambda: self.stop_recording(camera_id))
            timer.start(5 * 60 * 1000)  # 5 minutes
            self.active_recordings[camera_id]['timer'] = timer
            
            return True, "Motion recording started"
            
        except Exception as e:
            return False, f"Failed to start motion recording: {e}"
    
    def stop_recording(self, camera_id):
        """Stop recording for a camera"""
        if camera_id not in self.active_recordings:
            return False, "No active recording for this camera"
        
        try:
            recording_info = self.active_recordings[camera_id]
            recorder = recording_info['recorder']
            
            # Stop timer if exists
            if 'timer' in recording_info:
                recording_info['timer'].stop()
            
            # Stop recording
            recorder.stop_recording()
            recorder.wait()  # Wait for thread to finish
            
            return True, "Recording stopped successfully"
            
        except Exception as e:
            return False, f"Failed to stop recording: {e}"
    
    def stop_all_recordings(self):
        """Stop all active recordings"""
        for camera_id in list(self.active_recordings.keys()):
            self.stop_recording(camera_id)
    
    def update_frame(self, camera_id, frame):
        """Update frame for active recording"""
        if camera_id in self.active_recordings:
            recorder = self.active_recordings[camera_id]['recorder']
            recorder.set_frame(frame)
    
    def is_recording(self, camera_id):
        """Check if camera is currently recording"""
        return camera_id in self.active_recordings
    
    def get_recording_info(self, camera_id):
        """Get recording information for camera"""
        if camera_id in self.active_recordings:
            info = self.active_recordings[camera_id].copy()
            info.pop('recorder', None)  # Remove recorder object
            info.pop('timer', None)     # Remove timer object
            return info
        return None
    
    def schedule_recording(self, camera_id, start_time, duration_minutes, repeat_daily=False):
        """Schedule automatic recording"""
        try:
            schedule_id = f"{camera_id}_{start_time}_{duration_minutes}"
            
            def scheduled_recording_job():
                if not self.is_recording(camera_id):
                    self.start_manual_recording(camera_id, duration_minutes)
            
            if repeat_daily:
                schedule.every().day.at(start_time).do(scheduled_recording_job)
            else:
                # One-time schedule (simplified implementation)
                schedule.every().day.at(start_time).do(scheduled_recording_job)
            
            self.scheduled_recordings[schedule_id] = {
                'camera_id': camera_id,
                'start_time': start_time,
                'duration_minutes': duration_minutes,
                'repeat_daily': repeat_daily,
                'job': scheduled_recording_job
            }
            
            return True, "Recording scheduled successfully"
            
        except Exception as e:
            return False, f"Failed to schedule recording: {e}"
    
    def cancel_scheduled_recording(self, schedule_id):
        """Cancel scheduled recording"""
        if schedule_id in self.scheduled_recordings:
            schedule.cancel_job(self.scheduled_recordings[schedule_id]['job'])
            del self.scheduled_recordings[schedule_id]
            return True
        return False
    
    def run_scheduler(self):
        """Run the recording scheduler"""
        while self.scheduler_running:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    
    def on_recording_started(self, camera_id, filename):
        """Handle recording started event"""
        print(f"Recording started for camera {camera_id}: {filename}")
        
        # Log to database
        if camera_id in self.active_recordings:
            recording_info = self.active_recordings[camera_id]
            recording_data = {
                'camera_id': camera_id,
                'filename': filename,
                'file_path': recording_info['output_path'],
                'start_time': recording_info['start_time'].isoformat(),
                'recording_type': recording_info['type'],
                'motion_triggered': recording_info.get('motion_triggered', False)
            }
            
            recording_id = self.db.add_recording(recording_data)
            self.active_recordings[camera_id]['recording_id'] = recording_id
    
    def on_recording_stopped(self, camera_id, filename, duration):
        """Handle recording stopped event"""
        print(f"Recording stopped for camera {camera_id}: {filename} (Duration: {duration}s)")
        
        if camera_id in self.active_recordings:
            recording_info = self.active_recordings[camera_id]
            
            # Update database with final information
            if 'recording_id' in recording_info:
                # Get file size
                file_size = 0
                try:
                    file_size = os.path.getsize(recording_info['output_path'])
                except:
                    pass
                
                # Update recording in database (simplified - would need update method in DatabaseManager)
                # self.db.update_recording(recording_info['recording_id'], duration, file_size)
            
            # Clean up
            del self.active_recordings[camera_id]
    
    def on_recording_error(self, camera_id, error_message):
        """Handle recording error"""
        print(f"Recording error for camera {camera_id}: {error_message}")
        
        if camera_id in self.active_recordings:
            del self.active_recordings[camera_id]
    
    def get_recordings_list(self, camera_id=None, limit=50):
        """Get list of recordings from database"""
        return self.db.get_recordings(camera_id, limit)
    
    def cleanup(self):
        """Clean up resources"""
        self.scheduler_running = False
        self.stop_all_recordings()
        
        if self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
