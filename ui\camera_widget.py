#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Camera Widget for displaying live video feed
ويدجت الكاميرا لعرض البث المباشر
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                           QPushButton, QFrame, QSizePolicy, QMenu, QAction)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QSize
from PyQt5.QtGui import QPixmap, QFont, QPainter, QPen, QBrush, QColor
import cv2
import numpy as np
from datetime import datetime

class CameraWidget(QWidget):
    """ويدجت عرض الكاميرا"""
    
    # إشارات
    camera_clicked = pyqtSignal(int)  # camera_id
    start_recording = pyqtSignal(int)  # camera_id
    stop_recording = pyqtSignal(int)  # camera_id
    take_snapshot = pyqtSignal(int)  # camera_id
    
    def __init__(self, camera_id, camera_name="Camera", parent=None):
        super().__init__(parent)
        
        self.camera_id = camera_id
        self.camera_name = camera_name
        self.is_recording = False
        self.motion_detected = False
        self.connection_status = False
        self.last_frame = None

        # متغيرات التكبير والتصغير
        self.zoom_level = 1.0
        self.min_zoom = 0.5
        self.max_zoom = 3.0
        self.zoom_step = 0.2
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تايمر لتحديث الإطارات
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(33)  # ~30 FPS
        
        # تايمر لإخفاء حالة الحركة
        self.motion_timer = QTimer()
        self.motion_timer.timeout.connect(self.clear_motion_status)
        self.motion_timer.setSingleShot(True)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFixedSize(320, 240)
        self.setStyleSheet("""
            CameraWidget {
                border: 2px solid #555;
                border-radius: 8px;
                background-color: #2b2b2b;
            }
            CameraWidget:hover {
                border: 2px solid #2196F3;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(2)
        
        # شريط العنوان
        self.title_bar = self.create_title_bar()
        layout.addWidget(self.title_bar)
        
        # منطقة عرض الفيديو
        self.video_label = QLabel()
        self.video_label.setMinimumSize(300, 180)
        self.video_label.setStyleSheet("""
            QLabel {
                background-color: #1a1a1a;
                border: 1px solid #444;
                border-radius: 4px;
            }
        """)
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setText("لا يوجد إشارة")
        self.video_label.setScaledContents(True)
        
        layout.addWidget(self.video_label)
        
        # شريط الحالة
        self.status_bar = self.create_status_bar()
        layout.addWidget(self.status_bar)
        
        self.setLayout(layout)
        
        # قائمة السياق
        self.setup_context_menu()
    
    def create_title_bar(self):
        """إنشاء شريط العنوان"""
        title_frame = QFrame()
        title_frame.setFixedHeight(25)
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #3b3b3b;
                border-radius: 4px;
            }
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(8, 2, 8, 2)
        
        # اسم الكاميرا
        self.name_label = QLabel(self.camera_name)
        self.name_label.setStyleSheet("color: white; font-weight: bold;")
        self.name_label.setFont(QFont("Arial", 9))
        
        # مؤشر الحالة
        self.status_indicator = QLabel("●")
        self.status_indicator.setStyleSheet("color: #f44336; font-size: 12px;")  # أحمر = غير متصل
        
        # أزرار التحكم
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(2)

        # زر التكبير
        self.zoom_in_btn = QPushButton("🔍+")
        self.zoom_in_btn.setFixedSize(18, 18)
        self.zoom_in_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 9px;
                font-size: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        self.zoom_in_btn.clicked.connect(self.zoom_in)

        # زر التصغير
        self.zoom_out_btn = QPushButton("🔍-")
        self.zoom_out_btn.setFixedSize(18, 18)
        self.zoom_out_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 9px;
                font-size: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)
        self.zoom_out_btn.clicked.connect(self.zoom_out)

        # زر إعادة التعيين
        self.reset_zoom_btn = QPushButton("↻")
        self.reset_zoom_btn.setFixedSize(18, 18)
        self.reset_zoom_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 9px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
            QPushButton:pressed {
                background-color: #2E7D32;
            }
        """)
        self.reset_zoom_btn.clicked.connect(self.reset_zoom)

        # زر لقطة
        self.snapshot_btn = QPushButton("📷")
        self.snapshot_btn.setFixedSize(18, 18)
        self.snapshot_btn.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                border: none;
                border-radius: 9px;
                font-size: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #455A64;
            }
            QPushButton:pressed {
                background-color: #263238;
            }
        """)
        self.snapshot_btn.clicked.connect(lambda: self.take_snapshot.emit(self.camera_id))

        # زر التسجيل
        self.record_btn = QPushButton("🔴")
        self.record_btn.setFixedSize(18, 18)
        self.record_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #555;
                border-radius: 9px;
            }
        """)
        self.record_btn.clicked.connect(self.toggle_recording)

        controls_layout.addWidget(self.zoom_in_btn)
        controls_layout.addWidget(self.zoom_out_btn)
        controls_layout.addWidget(self.reset_zoom_btn)
        controls_layout.addWidget(self.snapshot_btn)
        controls_layout.addWidget(self.record_btn)

        layout.addWidget(self.name_label)
        layout.addStretch()
        layout.addWidget(self.status_indicator)
        layout.addLayout(controls_layout)
        
        title_frame.setLayout(layout)
        return title_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFixedHeight(20)
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #2b2b2b;
                border-radius: 4px;
            }
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(8, 2, 8, 2)
        
        # معلومات الدقة
        self.resolution_label = QLabel("--")
        self.resolution_label.setStyleSheet("color: #888; font-size: 10px;")
        
        # معلومات FPS
        self.fps_label = QLabel("0 FPS")
        self.fps_label.setStyleSheet("color: #888; font-size: 10px;")
        
        # مستوى التكبير
        self.zoom_label = QLabel("1.0x")
        self.zoom_label.setStyleSheet("color: #4CAF50; font-size: 10px; font-weight: bold;")

        # الوقت
        self.time_label = QLabel()
        self.time_label.setStyleSheet("color: #888; font-size: 10px;")
        self.update_time()

        layout.addWidget(self.resolution_label)
        layout.addWidget(self.zoom_label)
        layout.addStretch()
        layout.addWidget(self.fps_label)
        layout.addWidget(self.time_label)
        
        status_frame.setLayout(layout)
        return status_frame
    
    def setup_context_menu(self):
        """إعداد قائمة السياق"""
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def show_context_menu(self, position):
        """عرض قائمة السياق"""
        menu = QMenu(self)
        
        # إجراءات القائمة
        snapshot_action = QAction("📷 لقطة", self)
        snapshot_action.triggered.connect(lambda: self.take_snapshot.emit(self.camera_id))
        
        record_action = QAction("🔴 تسجيل" if not self.is_recording else "⏹️ إيقاف التسجيل", self)
        record_action.triggered.connect(self.toggle_recording)
        
        fullscreen_action = QAction("🔳 ملء الشاشة", self)
        fullscreen_action.triggered.connect(lambda: self.camera_clicked.emit(self.camera_id))
        
        properties_action = QAction("⚙️ خصائص", self)
        properties_action.triggered.connect(self.show_properties)
        
        menu.addAction(snapshot_action)
        menu.addAction(record_action)
        menu.addSeparator()
        menu.addAction(fullscreen_action)
        menu.addAction(properties_action)
        
        menu.exec_(self.mapToGlobal(position))
    
    def update_frame(self, frame):
        """تحديث الإطار المعروض"""
        if frame is not None:
            self.last_frame = frame.copy()

            # تحويل الإطار إلى QPixmap
            height, width, channel = frame.shape
            bytes_per_line = 3 * width

            # تحويل من BGR إلى RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # إنشاء QImage
            from PyQt5.QtGui import QImage
            q_image = QImage(rgb_frame.data, width, height, bytes_per_line, QImage.Format_RGB888)

            # تحويل إلى QPixmap وتطبيق التكبير
            pixmap = QPixmap.fromImage(q_image)

            # حساب الحجم مع التكبير
            label_size = self.video_label.size()
            scaled_size = label_size * self.zoom_level

            scaled_pixmap = pixmap.scaled(
                scaled_size,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )

            # عرض الإطار
            self.video_label.setPixmap(scaled_pixmap)

            # تحديث معلومات الدقة
            self.resolution_label.setText(f"{width}x{height}")

            # تحديث حالة الاتصال
            self.set_connection_status(True)
        else:
            # لا يوجد إطار
            self.video_label.setText("لا يوجد إشارة")
            self.set_connection_status(False)
    
    def set_connection_status(self, connected):
        """تعيين حالة الاتصال"""
        if self.connection_status != connected:
            self.connection_status = connected
            
            if connected:
                self.status_indicator.setStyleSheet("color: #4CAF50; font-size: 12px;")  # أخضر = متصل
            else:
                self.status_indicator.setStyleSheet("color: #f44336; font-size: 12px;")  # أحمر = غير متصل
    
    def set_motion_status(self, detected):
        """تعيين حالة كشف الحركة"""
        self.motion_detected = detected
        
        if detected:
            # تغيير لون الحدود للإشارة لكشف الحركة
            self.setStyleSheet("""
                CameraWidget {
                    border: 2px solid #FF5722;
                    border-radius: 8px;
                    background-color: #2b2b2b;
                }
            """)
            
            # إخفاء التأثير بعد 3 ثوان
            self.motion_timer.start(3000)
        else:
            self.clear_motion_status()
    
    def clear_motion_status(self):
        """إزالة حالة كشف الحركة"""
        self.motion_detected = False
        self.setStyleSheet("""
            CameraWidget {
                border: 2px solid #555;
                border-radius: 8px;
                background-color: #2b2b2b;
            }
            CameraWidget:hover {
                border: 2px solid #2196F3;
            }
        """)
    
    def toggle_recording(self):
        """تبديل حالة التسجيل"""
        if self.is_recording:
            self.stop_recording.emit(self.camera_id)
            self.set_recording_status(False)
        else:
            self.start_recording.emit(self.camera_id)
            self.set_recording_status(True)
    
    def set_recording_status(self, recording):
        """تعيين حالة التسجيل"""
        self.is_recording = recording
        
        if recording:
            self.record_btn.setText("⏹️")
            self.record_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    border: none;
                    font-size: 12px;
                    border-radius: 10px;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
            """)
        else:
            self.record_btn.setText("🔴")
            self.record_btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    border: none;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #555;
                    border-radius: 10px;
                }
            """)
    
    def update_display(self):
        """تحديث العرض"""
        # تحديث الوقت
        self.update_time()
        
        # تحديث FPS (يمكن تحسينه لاحقاً)
        if self.connection_status:
            self.fps_label.setText("25 FPS")
        else:
            self.fps_label.setText("0 FPS")
    
    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(current_time)
    
    def show_properties(self):
        """عرض خصائص الكاميرا"""
        from PyQt5.QtWidgets import QMessageBox
        
        properties = f"""
        اسم الكاميرا: {self.camera_name}
        معرف الكاميرا: {self.camera_id}
        حالة الاتصال: {'متصل' if self.connection_status else 'غير متصل'}
        حالة التسجيل: {'نشط' if self.is_recording else 'متوقف'}
        كشف الحركة: {'نشط' if self.motion_detected else 'غير نشط'}
        """
        
        QMessageBox.information(self, "خصائص الكاميرا", properties)
    
    def mouseDoubleClickEvent(self, event):
        """النقر المزدوج لملء الشاشة"""
        self.camera_clicked.emit(self.camera_id)
    
    def get_camera_id(self):
        """الحصول على معرف الكاميرا"""
        return self.camera_id
    
    def get_camera_name(self):
        """الحصول على اسم الكاميرا"""
        return self.camera_name
    
    def set_camera_name(self, name):
        """تعيين اسم الكاميرا"""
        self.camera_name = name
        self.name_label.setText(name)

    def zoom_in(self):
        """تكبير الكاميرا"""
        if self.zoom_level < self.max_zoom:
            self.zoom_level += self.zoom_step
            self.zoom_level = min(self.zoom_level, self.max_zoom)
            self.update_zoom_display()
            self.refresh_frame()
            print(f"🔍+ تكبير الكاميرا {self.camera_id} إلى {self.zoom_level:.1f}x")

    def zoom_out(self):
        """تصغير الكاميرا"""
        if self.zoom_level > self.min_zoom:
            self.zoom_level -= self.zoom_step
            self.zoom_level = max(self.zoom_level, self.min_zoom)
            self.update_zoom_display()
            self.refresh_frame()
            print(f"🔍- تصغير الكاميرا {self.camera_id} إلى {self.zoom_level:.1f}x")

    def reset_zoom(self):
        """إعادة تعيين التكبير"""
        self.zoom_level = 1.0
        self.update_zoom_display()
        self.refresh_frame()
        print(f"↻ إعادة تعيين تكبير الكاميرا {self.camera_id} إلى {self.zoom_level:.1f}x")

    def update_zoom_display(self):
        """تحديث عرض مستوى التكبير"""
        self.zoom_label.setText(f"{self.zoom_level:.1f}x")

        # تغيير لون المؤشر حسب مستوى التكبير
        if self.zoom_level > 1.0:
            self.zoom_label.setStyleSheet("color: #2196F3; font-size: 10px; font-weight: bold;")  # أزرق للتكبير
        elif self.zoom_level < 1.0:
            self.zoom_label.setStyleSheet("color: #FF9800; font-size: 10px; font-weight: bold;")  # برتقالي للتصغير
        else:
            self.zoom_label.setStyleSheet("color: #4CAF50; font-size: 10px; font-weight: bold;")  # أخضر للحجم الطبيعي

    def refresh_frame(self):
        """إعادة عرض الإطار الحالي مع التكبير الجديد"""
        if self.last_frame is not None:
            self.update_frame(self.last_frame)

    def get_zoom_level(self):
        """الحصول على مستوى التكبير الحالي"""
        return self.zoom_level

    def set_zoom_level(self, zoom_level):
        """تعيين مستوى التكبير"""
        self.zoom_level = max(self.min_zoom, min(zoom_level, self.max_zoom))
        self.update_zoom_display()
        self.refresh_frame()
