#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SmartPSS Pro - Safe Main Entry Point
نقطة دخول آمنة مع معالجة شاملة للأخطاء
"""

import sys
import os
import traceback
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def safe_import(module_name, package=None):
    """استيراد آمن للوحدات"""
    try:
        if package:
            module = __import__(f"{package}.{module_name}", fromlist=[module_name])
        else:
            module = __import__(module_name)
        return module, None
    except ImportError as e:
        return None, str(e)
    except Exception as e:
        return None, f"خطأ غير متوقع: {str(e)}"

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print(f"❌ إصدار Python غير مدعوم: {sys.version}")
        print("يرجى استخدام Python 3.8 أو أحدث")
        return False
    
    print(f"✅ إصدار Python مناسب: {sys.version}")
    return True

def check_and_install_requirements():
    """فحص وتثبيت المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    requirements = {
        'PyQt5': 'PyQt5',
        'numpy': 'numpy', 
        'psutil': 'psutil'
    }
    
    missing = []
    available = []
    
    for display_name, module_name in requirements.items():
        module, error = safe_import(module_name)
        if module:
            available.append(display_name)
            print(f"✅ {display_name}")
        else:
            missing.append(module_name)
            print(f"❌ {display_name} - {error}")
    
    if missing:
        print(f"\n⚠️ مكتبات مفقودة: {', '.join(missing)}")
        
        try:
            import subprocess
            print("🔄 محاولة تثبيت المكتبات المفقودة...")
            
            for package in missing:
                print(f"تثبيت {package}...")
                result = subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ تم تثبيت {package}")
                else:
                    print(f"❌ فشل تثبيت {package}: {result.stderr}")
                    
        except Exception as e:
            print(f"❌ خطأ في التثبيت التلقائي: {e}")
            print("يرجى تثبيت المكتبات يدوياً:")
            print(f"pip install {' '.join(missing)}")
            return False
    
    print(f"\n✅ المكتبات المتوفرة: {', '.join(available)}")
    return len(missing) == 0

def create_minimal_app():
    """إنشاء تطبيق أساسي آمن"""
    try:
        # استيراد PyQt5 بشكل آمن
        QtWidgets, error = safe_import('QtWidgets', 'PyQt5')
        if not QtWidgets:
            print(f"❌ فشل استيراد PyQt5: {error}")
            return None
        
        QtCore, _ = safe_import('QtCore', 'PyQt5')
        QtGui, _ = safe_import('QtGui', 'PyQt5')
        
        class SafeMainWindow(QtWidgets.QMainWindow):
            def __init__(self):
                super().__init__()
                self.init_ui()
            
            def init_ui(self):
                """تهيئة الواجهة الآمنة"""
                self.setWindowTitle("SmartPSS Pro - الوضع الآمن")
                self.setGeometry(200, 200, 500, 300)
                
                # الودجة المركزية
                central_widget = QtWidgets.QWidget()
                self.setCentralWidget(central_widget)
                
                # التخطيط
                layout = QtWidgets.QVBoxLayout()
                
                # العنوان
                title = QtWidgets.QLabel("SmartPSS Pro")
                title.setAlignment(QtCore.Qt.AlignCenter)
                title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2196F3; margin: 20px;")
                
                # الحالة
                status = QtWidgets.QLabel("✅ النظام يعمل في الوضع الآمن")
                status.setAlignment(QtCore.Qt.AlignCenter)
                status.setStyleSheet("font-size: 14px; color: #4CAF50; margin: 10px;")
                
                # معلومات النظام
                info_text = f"""
                إصدار Python: {sys.version.split()[0]}
                النظام: {os.name}
                المجلد: {current_dir}
                """
                
                info_label = QtWidgets.QLabel(info_text)
                info_label.setAlignment(QtCore.Qt.AlignCenter)
                info_label.setStyleSheet("font-size: 12px; color: #666; margin: 10px;")
                
                # أزرار الاختبار
                test_db_btn = QtWidgets.QPushButton("🗄️ اختبار قاعدة البيانات")
                test_db_btn.clicked.connect(self.test_database)
                
                test_settings_btn = QtWidgets.QPushButton("⚙️ اختبار الإعدادات")
                test_settings_btn.clicked.connect(self.test_settings)
                
                test_full_btn = QtWidgets.QPushButton("🚀 تشغيل النظام الكامل")
                test_full_btn.clicked.connect(self.launch_full_system)
                
                # إضافة العناصر
                layout.addWidget(title)
                layout.addWidget(status)
                layout.addWidget(info_label)
                layout.addWidget(test_db_btn)
                layout.addWidget(test_settings_btn)
                layout.addWidget(test_full_btn)
                layout.addStretch()
                
                central_widget.setLayout(layout)
                
                # تطبيق نمط
                self.setStyleSheet("""
                    QMainWindow {
                        background-color: #f5f5f5;
                    }
                    QPushButton {
                        background-color: #2196F3;
                        border: none;
                        border-radius: 6px;
                        padding: 10px;
                        font-size: 14px;
                        color: white;
                        margin: 5px;
                    }
                    QPushButton:hover {
                        background-color: #1976D2;
                    }
                    QPushButton:pressed {
                        background-color: #0D47A1;
                    }
                """)
            
            def test_database(self):
                """اختبار قاعدة البيانات"""
                try:
                    database_module, error = safe_import('database')
                    if database_module:
                        db = database_module.DatabaseManager()
                        QtWidgets.QMessageBox.information(self, "نجح", "✅ قاعدة البيانات تعمل بشكل صحيح!")
                    else:
                        QtWidgets.QMessageBox.warning(self, "تحذير", f"⚠️ لم يتم العثور على وحدة قاعدة البيانات:\n{error}")
                except Exception as e:
                    QtWidgets.QMessageBox.critical(self, "خطأ", f"❌ خطأ في قاعدة البيانات:\n{str(e)}")
            
            def test_settings(self):
                """اختبار الإعدادات"""
                try:
                    settings_module, error = safe_import('settings')
                    if settings_module:
                        settings = settings_module.SettingsManager()
                        QtWidgets.QMessageBox.information(self, "نجح", "✅ الإعدادات تعمل بشكل صحيح!")
                    else:
                        QtWidgets.QMessageBox.warning(self, "تحذير", f"⚠️ لم يتم العثور على وحدة الإعدادات:\n{error}")
                except Exception as e:
                    QtWidgets.QMessageBox.critical(self, "خطأ", f"❌ خطأ في الإعدادات:\n{str(e)}")
            
            def launch_full_system(self):
                """تشغيل النظام الكامل"""
                try:
                    # محاولة استيراد النافذة الرئيسية
                    main_window_module, error = safe_import('main_window', 'ui')
                    if main_window_module:
                        self.full_window = main_window_module.MainWindow()
                        self.full_window.show()
                        QtWidgets.QMessageBox.information(self, "نجح", "✅ تم تشغيل النظام الكامل!")
                    else:
                        QtWidgets.QMessageBox.warning(self, "تحذير", f"⚠️ لم يتم العثور على النافذة الرئيسية:\n{error}")
                except Exception as e:
                    QtWidgets.QMessageBox.critical(self, "خطأ", f"❌ خطأ في تشغيل النظام الكامل:\n{str(e)}\n\nالتفاصيل:\n{traceback.format_exc()}")
        
        return SafeMainWindow
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق: {e}")
        print(f"التفاصيل: {traceback.format_exc()}")
        return None

def main():
    """الدالة الرئيسية الآمنة"""
    print("=" * 60)
    print("🛡️ SmartPSS Pro - الوضع الآمن")
    print("=" * 60)
    
    try:
        # فحص إصدار Python
        if not check_python_version():
            input("\nاضغط Enter للخروج...")
            return 1
        
        # فحص المتطلبات
        if not check_and_install_requirements():
            print("\n❌ لم يتم تثبيت جميع المتطلبات")
            print("يرجى تثبيت المكتبات المطلوبة يدوياً:")
            print("pip install PyQt5 numpy psutil")
            input("\nاضغط Enter للخروج...")
            return 1
        
        # إنشاء التطبيق
        QtWidgets, _ = safe_import('QtWidgets', 'PyQt5')
        if not QtWidgets:
            print("❌ فشل في استيراد PyQt5")
            return 1
        
        app = QtWidgets.QApplication(sys.argv)
        app.setApplicationName("SmartPSS Pro Safe Mode")
        
        # إنشاء النافذة
        SafeMainWindow = create_minimal_app()
        if not SafeMainWindow:
            print("❌ فشل في إنشاء النافذة")
            return 1
        
        window = SafeMainWindow()
        window.show()
        
        print("✅ تم تشغيل الوضع الآمن بنجاح!")
        print("🎯 يمكنك الآن اختبار مكونات النظام")
        
        return app.exec_()
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف التطبيق بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"❌ خطأ فادح: {e}")
        print(f"التفاصيل: {traceback.format_exc()}")
        input("\nاضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
