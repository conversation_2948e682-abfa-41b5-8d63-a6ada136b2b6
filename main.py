#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SmartPSS-like Monitoring System
Main application entry point

A comprehensive camera monitoring system with:
- Live RTSP streaming
- Motion detection
- Video recording
- Multi-user authentication
- Arabic language support
- Dark theme interface
"""

import sys
import os
import logging
from datetime import datetime
from PyQt5.QtWidgets import <PERSON>App<PERSON>, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer, QTranslator, QLocale
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import our modules
from ui.main_window import MainWindow
from ui.login_dialog import LoginDialog
from database import DatabaseManager
from settings import SettingsManager
from user_manager import UserManager

class SmartPSSApplication(QApplication):
    """Main application class"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # Application info
        self.setApplicationName("SmartPSS")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("SmartPSS Systems")
        self.setOrganizationDomain("smartpss.local")
        
        # Initialize logging
        self.setup_logging()
        
        # Initialize managers
        self.db_manager = None
        self.settings_manager = None
        self.user_manager = None
        self.main_window = None
        
        # Setup application
        self.setup_application()
    
    def setup_logging(self):
        """Setup application logging"""
        try:
            # Create logs directory
            logs_dir = "logs"
            if not os.path.exists(logs_dir):
                os.makedirs(logs_dir)
            
            # Setup logging configuration
            log_filename = os.path.join(logs_dir, f"smartpss_{datetime.now().strftime('%Y%m%d')}.log")
            
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler(log_filename, encoding='utf-8'),
                    logging.StreamHandler(sys.stdout)
                ]
            )
            
            self.logger = logging.getLogger('SmartPSS')
            self.logger.info("SmartPSS Application Starting...")
            
        except Exception as e:
            print(f"Logging setup error: {e}")
            self.logger = logging.getLogger('SmartPSS')
    
    def setup_application(self):
        """Setup application configuration"""
        try:
            # Initialize database
            self.db_manager = DatabaseManager()
            self.logger.info("Database initialized")
            
            # Initialize settings
            self.settings_manager = SettingsManager()
            self.logger.info("Settings loaded")
            
            # Initialize user manager
            self.user_manager = UserManager()
            self.logger.info("User manager initialized")
            
            # Setup application appearance
            self.setup_appearance()
            
            # Setup language
            self.setup_language()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Application setup error: {e}")
            self.show_error("خطأ في تهيئة التطبيق", f"فشل في تهيئة التطبيق:\n{str(e)}")
            return False
    
    def setup_appearance(self):
        """Setup application appearance and theme"""
        try:
            # Set application font
            font = QFont("Arial", 10)
            font.setStyleHint(QFont.SansSerif)
            self.setFont(font)
            
            # Apply dark theme
            self.setStyle('Fusion')
            
            # Set dark palette
            dark_palette = QPalette()
            dark_palette.setColor(QPalette.Window, QColor(43, 43, 43))
            dark_palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
            dark_palette.setColor(QPalette.Base, QColor(64, 64, 64))
            dark_palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
            dark_palette.setColor(QPalette.ToolTipBase, QColor(0, 0, 0))
            dark_palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
            dark_palette.setColor(QPalette.Text, QColor(255, 255, 255))
            dark_palette.setColor(QPalette.Button, QColor(64, 64, 64))
            dark_palette.setColor(QPalette.ButtonText, QColor(255, 255, 255))
            dark_palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
            dark_palette.setColor(QPalette.Link, QColor(42, 130, 218))
            dark_palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
            dark_palette.setColor(QPalette.HighlightedText, QColor(0, 0, 0))
            
            self.setPalette(dark_palette)
            
            # Set application icon (if available)
            # self.setWindowIcon(QIcon('icons/app_icon.png'))
            
            self.logger.info("Application appearance configured")
            
        except Exception as e:
            self.logger.error(f"Appearance setup error: {e}")
    
    def setup_language(self):
        """Setup application language"""
        try:
            language = self.settings_manager.get_setting('language', 'ar')
            
            if language == 'ar':
                # Set right-to-left layout for Arabic
                self.setLayoutDirection(Qt.RightToLeft)
            else:
                self.setLayoutDirection(Qt.LeftToRight)
            
            self.logger.info(f"Language set to: {language}")
            
        except Exception as e:
            self.logger.error(f"Language setup error: {e}")
    
    def show_splash_screen(self):
        """Show application splash screen"""
        try:
            # Create splash screen (you would need to provide a splash image)
            # splash_pixmap = QPixmap('images/splash.png')
            # splash = QSplashScreen(splash_pixmap)
            # splash.show()
            
            # Show splash for 2 seconds
            # QTimer.singleShot(2000, splash.close)
            
            pass  # Skip splash screen for now
            
        except Exception as e:
            self.logger.error(f"Splash screen error: {e}")
    
    def show_login_screen(self):
        """Show login screen"""
        try:
            login_dialog = LoginDialog(self.user_manager)
            
            if login_dialog.exec_() == login_dialog.Accepted:
                self.logger.info("User logged in successfully")
                return True
            else:
                self.logger.info("Login cancelled")
                return False
                
        except Exception as e:
            self.logger.error(f"Login screen error: {e}")
            self.show_error("خطأ في تسجيل الدخول", f"فشل في عرض شاشة تسجيل الدخول:\n{str(e)}")
            return False
    
    def show_main_window(self):
        """Show main application window"""
        try:
            self.main_window = MainWindow()
            self.main_window.show()
            
            self.logger.info("Main window displayed")
            return True
            
        except Exception as e:
            self.logger.error(f"Main window error: {e}")
            self.show_error("خطأ في النافذة الرئيسية", f"فشل في عرض النافذة الرئيسية:\n{str(e)}")
            return False
    
    def show_error(self, title, message):
        """Show error message dialog"""
        try:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec_()
        except Exception as e:
            print(f"Error dialog error: {e}")
    
    def run(self):
        """Run the application"""
        try:
            self.logger.info("Starting SmartPSS application")
            
            # Show splash screen
            self.show_splash_screen()
            
            # Show login screen
            if not self.show_login_screen():
                self.logger.info("Application exit - login cancelled")
                return 0
            
            # Show main window
            if not self.show_main_window():
                self.logger.error("Failed to show main window")
                return 1
            
            # Start event loop
            self.logger.info("Application running")
            return self.exec_()
            
        except Exception as e:
            self.logger.error(f"Application run error: {e}")
            self.show_error("خطأ في التطبيق", f"حدث خطأ أثناء تشغيل التطبيق:\n{str(e)}")
            return 1
    
    def cleanup(self):
        """Cleanup application resources"""
        try:
            self.logger.info("Cleaning up application resources")
            
            if self.main_window:
                self.main_window.close()
            
            # Additional cleanup can be added here
            
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    try:
        import cv2
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import PyQt5
    except ImportError:
        missing_deps.append("PyQt5")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    if missing_deps:
        print("Missing dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nPlease install missing dependencies using:")
        print("pip install " + " ".join(missing_deps))
        return False
    
    return True

def create_directories():
    """Create necessary directories"""
    directories = [
        "videos",
        "logs", 
        "snapshots",
        "sounds",
        "resources",
        "resources/icons"
    ]
    
    for directory in directories:
        try:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"Created directory: {directory}")
        except Exception as e:
            print(f"Failed to create directory {directory}: {e}")

def main():
    """Main application entry point"""
    try:
        # Check dependencies
        if not check_dependencies():
            return 1
        
        # Create necessary directories
        create_directories()
        
        # Create and run application
        app = SmartPSSApplication(sys.argv)
        
        # Set up exception handling
        sys.excepthook = lambda exc_type, exc_value, exc_tb: app.logger.error(
            f"Unhandled exception: {exc_type.__name__}: {exc_value}"
        )
        
        # Run application
        exit_code = app.run()
        
        # Cleanup
        app.cleanup()
        
        return exit_code
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        return 0
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
