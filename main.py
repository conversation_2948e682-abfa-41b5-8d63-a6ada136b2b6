م تحل مشكلة
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SmartPSS Pro - Advanced Monitoring System
الإصدار المتطور من نظام المراقبة الذكي

نظام مراقبة متطور مع:
- بث مباشر عالي الجودة مع دعم 4K
- كشف حركة ذكي باستخدام الذكاء الاصطناعي
- تسجيل متقدم مع ضغط تلقائي
- مصادقة متعددة المستويات مع 2FA
- واجهة عربية حديثة مع Material Design
- نظام تنبيهات ذكي ومتطور
- تحليلات متقدمة ولوحة معلومات تفاعلية
- دعم التخزين السحابي والنسخ الاحتياطي
"""

import sys
import json
import logging
from datetime import datetime
from pathlib import Path
import platform
import psutil
import socket

# PyQt5 imports with error handling
try:
    from PyQt5.QtWidgets import (QApplication, QMessageBox, QSplashScreen,
                               QSystemTrayIcon, QMenu, QAction)
    from PyQt5.QtCore import (Qt, QTimer, QTranslator, QThread,
                            pyqtSignal, QSettings)
    from PyQt5.QtGui import (QFont, QPixmap, QPalette, QColor, QIcon,
                           QFontDatabase)
    PYQT5_AVAILABLE = True
except ImportError as e:
    print(f"خطأ في استيراد PyQt5: {e}")
    print("يرجى تثبيت PyQt5: pip install PyQt5")
    sys.exit(1)

# Add current directory to Python path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Import our enhanced modules
try:
    from database import DatabaseManager
    from settings import SettingsManager
    from user_manager import UserManager
    from alerts import AlertManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات الأساسية: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة")
    sys.exit(1)

# Import UI modules (will be imported when needed)
def import_ui_modules():
    """استيراد وحدات الواجهة عند الحاجة"""
    try:
        from ui.main_window import MainWindow
        from ui.login_dialog import LoginDialog
        return MainWindow, LoginDialog
    except ImportError as e:
        print(f"خطأ في استيراد وحدات الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return None, None
    except Exception as e:
        print(f"خطأ عام في استيراد وحدات الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return None, None

class PerformanceMonitor(QThread):
    """مراقب الأداء المتقدم"""

    performance_update = pyqtSignal(dict)
    performance_warning = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.running = False
        self.monitoring_interval = 5  # ثواني

    def run(self):
        """تشغيل مراقبة الأداء"""
        self.running = True
        while self.running:
            try:
                # جمع بيانات الأداء
                performance_data = {
                    'cpu_percent': psutil.cpu_percent(interval=1),
                    'memory_percent': psutil.virtual_memory().percent,
                    'memory_used': psutil.virtual_memory().used,
                    'memory_available': psutil.virtual_memory().available,
                    'disk_usage': psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent,
                    'network_sent': psutil.net_io_counters().bytes_sent,
                    'network_recv': psutil.net_io_counters().bytes_recv,
                    'timestamp': datetime.now()
                }

                # إرسال البيانات
                self.performance_update.emit(performance_data)

                # فحص التحذيرات
                if performance_data['cpu_percent'] > 80:
                    self.performance_warning.emit(f"استخدام المعالج مرتفع: {performance_data['cpu_percent']:.1f}%")

                if performance_data['memory_percent'] > 85:
                    self.performance_warning.emit(f"استخدام الذاكرة مرتفع: {performance_data['memory_percent']:.1f}%")

                if performance_data['disk_usage'] > 90:
                    self.performance_warning.emit(f"مساحة القرص منخفضة: {performance_data['disk_usage']:.1f}%")

                self.msleep(self.monitoring_interval * 1000)

            except Exception as e:
                print(f"خطأ في مراقبة الأداء: {e}")
                self.msleep(5000)

    def stop(self):
        """إيقاف مراقبة الأداء"""
        self.running = False
        self.wait()

class UpdateChecker(QThread):
    """فاحص التحديثات"""

    update_available = pyqtSignal(dict)
    update_error = pyqtSignal(str)

    def __init__(self, current_version):
        super().__init__()
        self.current_version = current_version
        self.update_url = "https://api.github.com/repos/smartpss/smartpss-pro/releases/latest"

    def run(self):
        """فحص التحديثات"""
        try:
            import requests
            response = requests.get(self.update_url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                latest_version = data.get('tag_name', '').replace('v', '')

                if self.is_newer_version(latest_version, self.current_version):
                    update_info = {
                        'version': latest_version,
                        'download_url': data.get('html_url'),
                        'release_notes': data.get('body', ''),
                        'published_at': data.get('published_at')
                    }
                    self.update_available.emit(update_info)
        except Exception as e:
            self.update_error.emit(f"خطأ في فحص التحديثات: {str(e)}")

    def is_newer_version(self, latest, current):
        """مقارنة الإصدارات"""
        try:
            latest_parts = [int(x) for x in latest.split('.')]
            current_parts = [int(x) for x in current.split('.')]

            for i in range(max(len(latest_parts), len(current_parts))):
                latest_part = latest_parts[i] if i < len(latest_parts) else 0
                current_part = current_parts[i] if i < len(current_parts) else 0

                if latest_part > current_part:
                    return True
                elif latest_part < current_part:
                    return False

            return False
        except:
            return False

class CrashHandler:
    """معالج الأخطاء والانهيارات"""

    def __init__(self, logger):
        self.logger = logger
        self.crash_reports_dir = Path("crash_reports")
        self.crash_reports_dir.mkdir(exist_ok=True)

    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """معالجة الاستثناءات غير المتوقعة"""
        import traceback

        # تجاهل استثناءات النظام العادية
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return

        # تسجيل الخطأ
        error_msg = f"خطأ غير متوقع: {exc_type.__name__}: {exc_value}"
        self.logger.critical(error_msg)

        # إنشاء تقرير الانهيار
        crash_report = {
            'timestamp': datetime.now().isoformat(),
            'error_type': exc_type.__name__,
            'error_message': str(exc_value),
            'traceback': traceback.format_exception(exc_type, exc_value, exc_traceback),
            'system_info': self.get_system_snapshot(),
            'application_state': self.get_application_state()
        }

        # حفظ تقرير الانهيار
        crash_file = self.crash_reports_dir / f"crash_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(crash_file, 'w', encoding='utf-8') as f:
                json.dump(crash_report, f, ensure_ascii=False, indent=2, default=str)
            self.logger.info(f"تم حفظ تقرير الانهيار: {crash_file}")
        except Exception as save_error:
            self.logger.error(f"فشل في حفظ تقرير الانهيار: {save_error}")

        # عرض رسالة للمستخدم
        try:
            from PyQt5.QtWidgets import QMessageBox
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Critical)
            msg.setWindowTitle("❌ خطأ في التطبيق")
            msg.setText("حدث خطأ غير متوقع في التطبيق")
            msg.setInformativeText("سيتم إعادة تشغيل التطبيق تلقائياً")
            msg.setDetailedText(f"{error_msg}\n\nتم حفظ تقرير الخطأ في:\n{crash_file}")
            msg.setStandardButtons(QMessageBox.Ok | QMessageBox.Close)
            msg.setDefaultButton(QMessageBox.Ok)

            result = msg.exec_()
            if result == QMessageBox.Close:
                sys.exit(1)

        except Exception as ui_error:
            print(f"خطأ في عرض رسالة الخطأ: {ui_error}")
            print(f"الخطأ الأصلي: {error_msg}")

    def get_application_state(self):
        """الحصول على حالة التطبيق الحالية"""
        try:
            return {
                'main_window_visible': hasattr(self, 'main_window') and self.main_window and self.main_window.isVisible(),
                'performance_monitor_running': hasattr(self, 'performance_monitor') and self.performance_monitor and self.performance_monitor.isRunning(),
                'system_tray_visible': hasattr(self, 'system_tray') and self.system_tray and self.system_tray.isVisible(),
                'active_cameras': 0,  # سيتم تحديثه لاحقاً
                'current_user': 'unknown'  # سيتم تحديثه لاحقاً
            }
        except Exception:
            return {'error': 'failed_to_get_state'}

    def get_system_snapshot(self):
        """أخذ لقطة من حالة النظام"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent if platform.system() != 'Windows' else psutil.disk_usage('C:').percent,
                'running_processes': len(psutil.pids()),
                'uptime': datetime.now() - datetime.fromtimestamp(psutil.boot_time())
            }
        except:
            return {}

class SmartPSSProApplication(QApplication):
    """تطبيق SmartPSS Pro المتطور"""

    # إشارات التطبيق
    system_ready = pyqtSignal()
    system_error = pyqtSignal(str)
    update_available = pyqtSignal(str)

    def __init__(self, argv):
        super().__init__(argv)

        # معلومات التطبيق المحدثة
        self.setApplicationName("SmartPSS Pro")
        self.setApplicationVersion("2.1.0")
        self.setApplicationDisplayName("نظام المراقبة الذكي المتطور")
        self.setOrganizationName("SmartPSS Advanced Systems")
        self.setOrganizationDomain("smartpss-pro.local")

        # إعدادات النظام
        self.system_info = self.get_system_info()
        self.performance_monitor = None
        self.update_checker = None
        self.crash_handler = None

        # تهيئة السجلات المتقدمة
        self.setup_advanced_logging()

        # تهيئة المدراء
        self.db_manager = None
        self.settings_manager = None
        self.user_manager = None
        self.alert_manager = None
        self.main_window = None
        self.splash_screen = None
        self.system_tray = None

        # إعداد التطبيق
        self.setup_application()

        # بدء مراقبة الأداء
        self.start_performance_monitoring()
    
    def get_system_info(self):
        """الحصول على معلومات النظام"""
        try:
            return {
                'platform': platform.system(),
                'platform_version': platform.version(),
                'architecture': platform.architecture()[0],
                'processor': platform.processor(),
                'hostname': socket.gethostname(),
                'python_version': platform.python_version(),
                'memory_total': psutil.virtual_memory().total,
                'disk_total': psutil.disk_usage('/').total if platform.system() != 'Windows' else psutil.disk_usage('C:').total,
                'cpu_count': psutil.cpu_count(),
                'boot_time': datetime.fromtimestamp(psutil.boot_time())
            }
        except Exception as e:
            return {'error': str(e)}

    def setup_advanced_logging(self):
        """إعداد نظام السجلات المتقدم"""
        try:
            # إنشاء مجلد السجلات
            logs_dir = Path("logs")
            logs_dir.mkdir(exist_ok=True)

            # إعداد أسماء ملفات السجل
            today = datetime.now().strftime('%Y%m%d')
            log_files = {
                'main': logs_dir / f"smartpss_main_{today}.log",
                'error': logs_dir / f"smartpss_error_{today}.log",
                'performance': logs_dir / f"smartpss_performance_{today}.log",
                'security': logs_dir / f"smartpss_security_{today}.log"
            }

            # إعداد التنسيق المتقدم
            detailed_formatter = logging.Formatter(
                '%(asctime)s | %(name)s | %(levelname)s | %(funcName)s:%(lineno)d | %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )

            # إعداد معالجات السجل
            self.logger = logging.getLogger('SmartPSS-Pro')
            self.logger.setLevel(logging.DEBUG)

            # معالج الملف الرئيسي
            main_handler = logging.FileHandler(log_files['main'], encoding='utf-8')
            main_handler.setLevel(logging.INFO)
            main_handler.setFormatter(detailed_formatter)

            # معالج ملف الأخطاء
            error_handler = logging.FileHandler(log_files['error'], encoding='utf-8')
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(detailed_formatter)

            # معالج وحدة التحكم
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)
            console_formatter = logging.Formatter('%(levelname)s: %(message)s')
            console_handler.setFormatter(console_formatter)

            # إضافة المعالجات
            self.logger.addHandler(main_handler)
            self.logger.addHandler(error_handler)
            self.logger.addHandler(console_handler)

            # تسجيل بداية التطبيق
            self.logger.info("=" * 60)
            self.logger.info("SmartPSS Pro Application Starting...")
            self.logger.info(f"Version: {self.applicationVersion()}")
            self.logger.info(f"System: {self.system_info.get('platform', 'Unknown')}")
            self.logger.info(f"Python: {self.system_info.get('python_version', 'Unknown')}")
            self.logger.info("=" * 60)

        except Exception as e:
            print(f"خطأ في إعداد السجلات: {e}")
            self.logger = logging.getLogger('SmartPSS-Pro')

    def start_performance_monitoring(self):
        """بدء مراقبة الأداء"""
        try:
            self.performance_monitor = PerformanceMonitor()
            self.performance_monitor.start()
            self.logger.info("تم بدء مراقبة الأداء")
        except Exception as e:
            self.logger.error(f"خطأ في بدء مراقبة الأداء: {e}")
    
    def setup_application(self):
        """إعداد التطبيق المتقدم"""
        try:
            # تهيئة قاعدة البيانات المحسنة
            self.db_manager = DatabaseManager()
            self.logger.info("تم تهيئة قاعدة البيانات")

            # تهيئة مدير الإعدادات المتقدم
            self.settings_manager = SettingsManager()
            self.logger.info("تم تحميل الإعدادات")

            # تهيئة مدير المستخدمين مع الأمان المحسن
            self.user_manager = UserManager()
            self.logger.info("تم تهيئة مدير المستخدمين")

            # تهيئة مدير التنبيهات
            self.alert_manager = AlertManager()
            self.logger.info("تم تهيئة نظام التنبيهات")

            # إعداد معالج الأخطاء
            self.crash_handler = CrashHandler(self.logger)
            sys.excepthook = self.crash_handler.handle_exception

            # إعداد مظهر التطبيق المحسن
            self.setup_advanced_appearance()

            # إعداد اللغة والترجمة
            self.setup_language_and_translation()

            # إعداد شريط النظام
            self.setup_system_tray()

            # بدء فاحص التحديثات
            self.start_update_checker()

            # تحسين الأداء
            self.optimize_performance()

            return True

        except Exception as e:
            self.logger.error(f"خطأ في إعداد التطبيق: {e}")
            self.show_error("خطأ في تهيئة التطبيق", f"فشل في تهيئة التطبيق:\n{str(e)}")
            return False
    
    def setup_advanced_appearance(self):
        """إعداد المظهر المتقدم مع Material Design"""
        try:
            # تحميل الخطوط العربية المحسنة
            self.load_arabic_fonts()

            # تطبيق نمط Material Design المظلم
            self.setStyle('Fusion')

            # إنشاء لوحة ألوان متقدمة
            advanced_palette = QPalette()

            # ألوان Material Design المظلمة
            colors = {
                'primary': QColor(33, 150, 243),      # أزرق Material
                'primary_dark': QColor(25, 118, 210), # أزرق داكن
                'accent': QColor(255, 193, 7),        # أصفر ذهبي
                'background': QColor(18, 18, 18),     # خلفية داكنة جداً
                'surface': QColor(33, 33, 33),        # سطح داكن
                'error': QColor(244, 67, 54),         # أحمر خطأ
                'success': QColor(76, 175, 80),       # أخضر نجاح
                'warning': QColor(255, 152, 0),       # برتقالي تحذير
                'text_primary': QColor(255, 255, 255), # نص أساسي
                'text_secondary': QColor(158, 158, 158) # نص ثانوي
            }

            # تطبيق الألوان على اللوحة
            advanced_palette.setColor(QPalette.Window, colors['background'])
            advanced_palette.setColor(QPalette.WindowText, colors['text_primary'])
            advanced_palette.setColor(QPalette.Base, colors['surface'])
            advanced_palette.setColor(QPalette.AlternateBase, QColor(45, 45, 45))
            advanced_palette.setColor(QPalette.ToolTipBase, colors['surface'])
            advanced_palette.setColor(QPalette.ToolTipText, colors['text_primary'])
            advanced_palette.setColor(QPalette.Text, colors['text_primary'])
            advanced_palette.setColor(QPalette.Button, colors['surface'])
            advanced_palette.setColor(QPalette.ButtonText, colors['text_primary'])
            advanced_palette.setColor(QPalette.BrightText, colors['accent'])
            advanced_palette.setColor(QPalette.Link, colors['primary'])
            advanced_palette.setColor(QPalette.Highlight, colors['primary'])
            advanced_palette.setColor(QPalette.HighlightedText, colors['text_primary'])

            self.setPalette(advanced_palette)

            # تطبيق أنماط CSS متقدمة
            self.setStyleSheet(self.get_advanced_stylesheet())

            # تعيين أيقونة التطبيق
            self.set_application_icon()

            self.logger.info("تم إعداد المظهر المتقدم")

        except Exception as e:
            self.logger.error(f"خطأ في إعداد المظهر: {e}")

    def load_arabic_fonts(self):
        """تحميل الخطوط العربية المحسنة"""
        try:
            # قائمة الخطوط العربية المفضلة
            arabic_fonts = [
                "Segoe UI",
                "Tahoma",
                "Arial Unicode MS",
                "Microsoft Sans Serif",
                "Calibri"
            ]

            # البحث عن أفضل خط متاح
            font_db = QFontDatabase()
            available_fonts = font_db.families()

            selected_font = "Arial"  # الخط الافتراضي
            for font_name in arabic_fonts:
                if font_name in available_fonts:
                    selected_font = font_name
                    break

            # إنشاء خط محسن للعربية
            app_font = QFont(selected_font, 10)
            app_font.setStyleHint(QFont.SansSerif)
            app_font.setWeight(QFont.Normal)
            app_font.setKerning(True)

            self.setFont(app_font)
            self.logger.info(f"تم تحميل الخط: {selected_font}")

        except Exception as e:
            self.logger.error(f"خطأ في تحميل الخطوط: {e}")

    def get_advanced_stylesheet(self):
        """الحصول على أنماط CSS متقدمة"""
        return """
        /* Material Design Dark Theme */
        QMainWindow {
            background-color: #121212;
            color: #ffffff;
        }

        QWidget {
            background-color: #121212;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Arial;
        }

        /* أزرار محسنة */
        QPushButton {
            background-color: #2196F3;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 500;
            color: white;
            min-height: 20px;
        }

        QPushButton:hover {
            background-color: #1976D2;
            box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
        }

        QPushButton:pressed {
            background-color: #0D47A1;
            transform: translateY(1px);
        }

        QPushButton:disabled {
            background-color: #424242;
            color: #9E9E9E;
        }

        /* حقول الإدخال المحسنة */
        QLineEdit, QTextEdit, QSpinBox, QComboBox {
            background-color: #212121;
            border: 2px solid #424242;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            color: #ffffff;
        }

        QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QComboBox:focus {
            border-color: #2196F3;
            box-shadow: 0 0 8px rgba(33, 150, 243, 0.3);
        }

        /* شريط القوائم المحسن */
        QMenuBar {
            background-color: #1E1E1E;
            border-bottom: 1px solid #424242;
            padding: 4px;
        }

        QMenuBar::item {
            background-color: transparent;
            padding: 8px 16px;
            border-radius: 4px;
        }

        QMenuBar::item:selected {
            background-color: #2196F3;
        }

        /* شريط الأدوات المحسن */
        QToolBar {
            background-color: #1E1E1E;
            border: none;
            spacing: 8px;
            padding: 8px;
        }

        QToolBar::separator {
            background-color: #424242;
            width: 1px;
            margin: 4px;
        }

        /* التبويبات المحسنة */
        QTabWidget::pane {
            border: 1px solid #424242;
            background-color: #121212;
            border-radius: 8px;
        }

        QTabBar::tab {
            background-color: #212121;
            color: #ffffff;
            padding: 12px 24px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        QTabBar::tab:selected {
            background-color: #2196F3;
        }

        QTabBar::tab:hover {
            background-color: #1976D2;
        }

        /* شريط التمرير المحسن */
        QScrollBar:vertical {
            background-color: #212121;
            width: 12px;
            border-radius: 6px;
        }

        QScrollBar::handle:vertical {
            background-color: #424242;
            border-radius: 6px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #2196F3;
        }

        /* مربعات الاختيار المحسنة */
        QCheckBox {
            color: #ffffff;
            spacing: 8px;
        }

        QCheckBox::indicator {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 2px solid #424242;
            background-color: #212121;
        }

        QCheckBox::indicator:checked {
            background-color: #2196F3;
            border-color: #2196F3;
        }

        /* شريط الحالة المحسن */
        QStatusBar {
            background-color: #1E1E1E;
            border-top: 1px solid #424242;
            color: #ffffff;
        }
        """

    def set_application_icon(self):
        """تعيين أيقونة التطبيق"""
        try:
            icon_path = Path("resources/icons/app_icon.png")
            if icon_path.exists():
                self.setWindowIcon(QIcon(str(icon_path)))
            else:
                # إنشاء أيقونة افتراضية
                self.create_default_icon()
        except Exception as e:
            self.logger.error(f"خطأ في تعيين الأيقونة: {e}")

    def create_default_icon(self):
        """إنشاء أيقونة افتراضية"""
        try:
            # إنشاء أيقونة بسيطة باستخدام النص
            pixmap = QPixmap(64, 64)
            pixmap.fill(QColor(33, 150, 243))
            self.setWindowIcon(QIcon(pixmap))
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الأيقونة الافتراضية: {e}")
    
    def setup_language_and_translation(self):
        """إعداد اللغة والترجمة المتقدمة"""
        try:
            language = self.settings_manager.get_setting('language', 'ar')

            # إعداد اتجاه النص
            if language == 'ar':
                self.setLayoutDirection(Qt.RightToLeft)
            else:
                self.setLayoutDirection(Qt.LeftToRight)

            # تحميل ملفات الترجمة
            self.load_translations(language)

            self.logger.info(f"تم إعداد اللغة: {language}")

        except Exception as e:
            self.logger.error(f"خطأ في إعداد اللغة: {e}")

    def load_translations(self, language):
        """تحميل ملفات الترجمة"""
        try:
            translator = QTranslator()
            translation_file = f"translations/smartpss_{language}.qm"

            if Path(translation_file).exists():
                if translator.load(translation_file):
                    self.installTranslator(translator)
                    self.logger.info(f"تم تحميل ملف الترجمة: {translation_file}")
                else:
                    self.logger.warning(f"فشل في تحميل ملف الترجمة: {translation_file}")
            else:
                self.logger.info(f"ملف الترجمة غير موجود: {translation_file}")

        except Exception as e:
            self.logger.error(f"خطأ في تحميل الترجمة: {e}")

    def setup_system_tray(self):
        """إعداد شريط النظام المتقدم"""
        try:
            if QSystemTrayIcon.isSystemTrayAvailable():
                self.system_tray = QSystemTrayIcon()

                # تعيين الأيقونة
                icon_path = Path("resources/icons/tray_icon.png")
                if icon_path.exists():
                    self.system_tray.setIcon(QIcon(str(icon_path)))
                else:
                    # أيقونة افتراضية
                    pixmap = QPixmap(16, 16)
                    pixmap.fill(QColor(33, 150, 243))
                    self.system_tray.setIcon(QIcon(pixmap))

                # إنشاء قائمة السياق
                tray_menu = QMenu()

                # إضافة العناصر
                show_action = QAction("إظهار SmartPSS Pro", None)
                show_action.triggered.connect(self.show_main_window)
                tray_menu.addAction(show_action)

                tray_menu.addSeparator()

                performance_action = QAction("مراقب الأداء", None)
                performance_action.triggered.connect(self.show_performance_monitor)
                tray_menu.addAction(performance_action)

                settings_action = QAction("الإعدادات", None)
                settings_action.triggered.connect(self.show_settings)
                tray_menu.addAction(settings_action)

                tray_menu.addSeparator()

                quit_action = QAction("إنهاء", None)
                quit_action.triggered.connect(self.quit_application)
                tray_menu.addAction(quit_action)

                self.system_tray.setContextMenu(tray_menu)
                self.system_tray.show()

                # ربط الإشارات
                self.system_tray.activated.connect(self.tray_icon_activated)

                self.logger.info("تم إعداد شريط النظام")
            else:
                self.logger.warning("شريط النظام غير متاح")

        except Exception as e:
            self.logger.error(f"خطأ في إعداد شريط النظام: {e}")

    def start_update_checker(self):
        """بدء فاحص التحديثات"""
        try:
            self.update_checker = UpdateChecker(self.applicationVersion())
            self.update_checker.update_available.connect(self.handle_update_available)
            self.update_checker.update_error.connect(self.handle_update_error)

            # فحص التحديثات بعد 30 ثانية من البدء
            QTimer.singleShot(30000, self.update_checker.start)

            self.logger.info("تم بدء فاحص التحديثات")

        except Exception as e:
            self.logger.error(f"خطأ في بدء فاحص التحديثات: {e}")

    def optimize_performance(self):
        """تحسين أداء التطبيق"""
        try:
            # تحسين استخدام الذاكرة
            self.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings, True)
            self.setAttribute(Qt.AA_NativeWindows, False)

            # تحسين الرسم
            self.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

            # تحسين الخطوط
            self.setAttribute(Qt.AA_UseDesktopOpenGL, True)

            self.logger.info("تم تحسين أداء التطبيق")

        except Exception as e:
            self.logger.error(f"خطأ في تحسين الأداء: {e}")

    def handle_update_available(self, update_info):
        """معالجة توفر تحديث جديد"""
        try:
            message = f"تحديث جديد متاح: الإصدار {update_info['version']}\n\nهل تريد تحميله؟"

            reply = QMessageBox.question(
                None,
                "تحديث متاح",
                message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # فتح رابط التحميل
                import webbrowser
                webbrowser.open(update_info['download_url'])

        except Exception as e:
            self.logger.error(f"خطأ في معالجة التحديث: {e}")

    def handle_update_error(self, error_message):
        """معالجة خطأ فحص التحديثات"""
        self.logger.warning(f"خطأ في فحص التحديثات: {error_message}")

    def tray_icon_activated(self, reason):
        """معالجة تفعيل أيقونة شريط النظام"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show_main_window()

    def show_main_window(self):
        """إظهار النافذة الرئيسية"""
        if self.main_window:
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()

    def show_performance_monitor(self):
        """إظهار مراقب الأداء"""
        # سيتم تنفيذها لاحقاً
        pass

    def show_settings(self):
        """إظهار نافذة الإعدادات"""
        # سيتم تنفيذها لاحقاً
        pass

    def quit_application(self):
        """إنهاء التطبيق"""
        self.cleanup()
        self.quit()
    
    def show_advanced_splash_screen(self):
        """عرض شاشة البداية المتطورة"""
        try:
            # إنشاء شاشة البداية
            splash_pixmap = self.create_splash_pixmap()
            self.splash_screen = QSplashScreen(splash_pixmap)

            # تطبيق تأثيرات بصرية
            self.splash_screen.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
            self.splash_screen.setAttribute(Qt.WA_TranslucentBackground)

            # عرض الشاشة
            self.splash_screen.show()

            # رسائل التحميل
            loading_messages = [
                "تهيئة النظام...",
                "تحميل قاعدة البيانات...",
                "إعداد الكاميرات...",
                "تفعيل كشف الحركة...",
                "تحضير الواجهة...",
                "جاهز للاستخدام!"
            ]

            # عرض رسائل التحميل تدريجياً
            for i, message in enumerate(loading_messages):
                QTimer.singleShot(i * 500, lambda msg=message: self.update_splash_message(msg))

            # إخفاء الشاشة بعد 3 ثوانٍ
            QTimer.singleShot(3000, self.hide_splash_screen)

            self.logger.info("تم عرض شاشة البداية")

        except Exception as e:
            self.logger.error(f"خطأ في شاشة البداية: {e}")

    def create_splash_pixmap(self):
        """إنشاء صورة شاشة البداية"""
        try:
            # إنشاء صورة مخصصة
            pixmap = QPixmap(600, 400)
            pixmap.fill(QColor(18, 18, 18))

            from PyQt5.QtGui import QPainter, QLinearGradient, QBrush, QPen

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # إنشاء تدرج لوني
            gradient = QLinearGradient(0, 0, 600, 400)
            gradient.setColorAt(0, QColor(33, 150, 243))
            gradient.setColorAt(1, QColor(25, 118, 210))

            # رسم الخلفية
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(Qt.NoPen))
            painter.drawRect(0, 0, 600, 400)

            # رسم النص
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.setFont(QFont("Arial", 24, QFont.Bold))
            painter.drawText(50, 150, "SmartPSS Pro")

            painter.setFont(QFont("Arial", 14))
            painter.drawText(50, 180, "نظام المراقبة الذكي المتطور")

            painter.setFont(QFont("Arial", 12))
            painter.drawText(50, 350, "الإصدار 2.0.0")

            painter.end()

            return pixmap

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء صورة البداية: {e}")
            # إرجاع صورة بسيطة في حالة الخطأ
            simple_pixmap = QPixmap(600, 400)
            simple_pixmap.fill(QColor(33, 150, 243))
            return simple_pixmap

    def update_splash_message(self, message):
        """تحديث رسالة شاشة البداية"""
        if self.splash_screen:
            self.splash_screen.showMessage(
                message,
                Qt.AlignBottom | Qt.AlignCenter,
                QColor(255, 255, 255)
            )

    def hide_splash_screen(self):
        """إخفاء شاشة البداية"""
        if self.splash_screen:
            self.splash_screen.close()
            self.splash_screen = None
    
    def show_enhanced_login_screen(self):
        """عرض شاشة تسجيل الدخول المحسنة"""
        try:
            # إخفاء شاشة البداية قبل عرض تسجيل الدخول
            self.hide_splash_screen()

            # استيراد وحدة تسجيل الدخول
            _, LoginDialog = import_ui_modules()
            if LoginDialog is None:
                self.logger.error("فشل في استيراد وحدة تسجيل الدخول")
                return False

            login_dialog = LoginDialog(self.user_manager)

            # تطبيق التحسينات البصرية
            login_dialog.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
            login_dialog.setAttribute(Qt.WA_DeleteOnClose)

            # ربط الإشارات
            if hasattr(login_dialog, 'login_successful'):
                login_dialog.login_successful.connect(self.handle_successful_login)

            result = login_dialog.exec_()

            if result == login_dialog.Accepted:
                self.logger.info("تم تسجيل الدخول بنجاح")
                return True
            else:
                self.logger.info("تم إلغاء تسجيل الدخول")
                return False

        except Exception as e:
            self.logger.error(f"خطأ في شاشة تسجيل الدخول: {e}")
            self.show_error("خطأ في تسجيل الدخول", f"فشل في عرض شاشة تسجيل الدخول:\n{str(e)}")
            return False

    def show_enhanced_main_window(self):
        """عرض النافذة الرئيسية المحسنة"""
        try:
            # استيراد وحدة النافذة الرئيسية
            MainWindow, _ = import_ui_modules()
            if MainWindow is None:
                self.logger.error("فشل في استيراد وحدة النافذة الرئيسية")
                return False

            self.main_window = MainWindow()

            # تطبيق التحسينات
            self.setup_main_window_enhancements()

            # ربط الإشارات
            self.connect_main_window_signals()

            # عرض النافذة
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()

            self.logger.info("تم عرض النافذة الرئيسية")
            return True

        except Exception as e:
            self.logger.error(f"خطأ في النافذة الرئيسية: {e}")
            self.show_error("خطأ في النافذة الرئيسية", f"فشل في عرض النافذة الرئيسية:\n{str(e)}")
            return False

    def handle_successful_login(self, user_info):
        """معالجة تسجيل الدخول الناجح"""
        try:
            self.logger.info(f"تم تسجيل دخول المستخدم: {user_info.get('username', 'غير معروف')}")

            # تسجيل معلومات إضافية
            self.logger.info(f"دور المستخدم: {user_info.get('role', 'غير محدد')}")
            self.logger.info(f"الصلاحيات: {len(user_info.get('permissions', []))}")

        except Exception as e:
            self.logger.error(f"خطأ في معالجة تسجيل الدخول: {e}")

    def setup_main_window_enhancements(self):
        """إعداد تحسينات النافذة الرئيسية"""
        try:
            if self.main_window:
                # تطبيق الأنماط المتقدمة
                self.main_window.setStyleSheet(self.get_advanced_stylesheet())

                # تحسين الأداء
                self.main_window.setAttribute(Qt.WA_OpaquePaintEvent, True)
                self.main_window.setAttribute(Qt.WA_NoSystemBackground, True)

                # إعداد الحد الأدنى لحجم النافذة
                self.main_window.setMinimumSize(1024, 768)

                # تذكر حجم النافذة
                settings = QSettings()
                if settings.contains("window_geometry"):
                    self.main_window.restoreGeometry(settings.value("window_geometry"))

        except Exception as e:
            self.logger.error(f"خطأ في تحسينات النافذة الرئيسية: {e}")

    def connect_main_window_signals(self):
        """ربط إشارات النافذة الرئيسية"""
        try:
            if self.main_window:
                # ربط إشارات الإغلاق
                self.main_window.closeEvent = self.handle_main_window_close

                # ربط إشارات مراقبة الأداء
                if self.performance_monitor:
                    self.performance_monitor.performance_update.connect(
                        self.main_window.update_performance_info
                    )
                    self.performance_monitor.performance_warning.connect(
                        self.main_window.show_performance_warning
                    )

        except Exception as e:
            self.logger.error(f"خطأ في ربط إشارات النافذة الرئيسية: {e}")

    def handle_main_window_close(self, event):
        """معالجة إغلاق النافذة الرئيسية"""
        try:
            # حفظ حجم النافذة
            settings = QSettings()
            settings.setValue("window_geometry", self.main_window.saveGeometry())

            # التحقق من إعدادات التصغير
            minimize_to_tray = self.settings_manager.get_setting('minimize_to_tray', True)

            if minimize_to_tray and self.system_tray and self.system_tray.isVisible():
                # تصغير إلى شريط النظام
                event.ignore()
                self.main_window.hide()

                if self.system_tray:
                    self.system_tray.showMessage(
                        "SmartPSS Pro",
                        "تم تصغير التطبيق إلى شريط النظام",
                        QSystemTrayIcon.Information,
                        2000
                    )
            else:
                # إغلاق التطبيق
                event.accept()
                self.cleanup()
                self.quit()

        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"خطأ في معالجة إغلاق النافذة: {e}")
            else:
                print(f"خطأ في معالجة إغلاق النافذة: {e}")
            event.accept()
    
    def show_error(self, title, message):
        """Show error message dialog"""
        try:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec_()
        except Exception as e:
            print(f"Error dialog error: {e}")
    
    def run(self):
        """تشغيل التطبيق المتطور"""
        try:
            self.logger.info("بدء تشغيل SmartPSS Pro")

            # عرض شاشة البداية المتطورة
            self.show_advanced_splash_screen()

            # تأخير قصير للسماح بعرض شاشة البداية
            self.processEvents()

            # عرض شاشة تسجيل الدخول المحسنة
            if not self.show_enhanced_login_screen():
                self.logger.info("تم إلغاء تسجيل الدخول - إنهاء التطبيق")
                return 0

            # عرض النافذة الرئيسية المحسنة
            if not self.show_enhanced_main_window():
                self.logger.error("فشل في عرض النافذة الرئيسية")
                return 1

            # بدء حلقة الأحداث
            self.logger.info("SmartPSS Pro يعمل الآن")

            # إظهار إشعار في شريط النظام
            if self.system_tray:
                self.system_tray.showMessage(
                    "SmartPSS Pro",
                    "تم بدء تشغيل النظام بنجاح",
                    QSystemTrayIcon.Information,
                    3000
                )

            return self.exec_()

        except Exception as e:
            self.logger.error(f"خطأ في تشغيل التطبيق: {e}")
            self.show_error("خطأ في التطبيق", f"حدث خطأ أثناء تشغيل التطبيق:\n{str(e)}")
            return 1
    
    def cleanup(self):
        """تنظيف موارد التطبيق المتقدم"""
        try:
            self.logger.info("بدء تنظيف موارد التطبيق")

            # إيقاف مراقب الأداء
            if self.performance_monitor and self.performance_monitor.isRunning():
                self.performance_monitor.stop()
                self.logger.info("تم إيقاف مراقب الأداء")

            # إيقاف فاحص التحديثات
            if self.update_checker and self.update_checker.isRunning():
                self.update_checker.terminate()
                self.update_checker.wait(3000)
                self.logger.info("تم إيقاف فاحص التحديثات")

            # إغلاق النافذة الرئيسية
            if self.main_window:
                try:
                    # حفظ الإعدادات قبل الإغلاق
                    settings = QSettings()
                    settings.setValue("window_geometry", self.main_window.saveGeometry())

                    # إغلاق النافذة
                    self.main_window.close()
                    self.main_window = None
                    self.logger.info("تم إغلاق النافذة الرئيسية")
                except Exception as e:
                    self.logger.error(f"خطأ في إغلاق النافذة الرئيسية: {e}")

            # إغلاق مدير التنبيهات
            if self.alert_manager:
                try:
                    self.alert_manager.stop()
                    self.logger.info("تم إيقاف مدير التنبيهات")
                except Exception as e:
                    self.logger.error(f"خطأ في إيقاف مدير التنبيهات: {e}")

            # إخفاء أيقونة شريط النظام
            if self.system_tray:
                self.system_tray.hide()
                self.system_tray = None
                self.logger.info("تم إخفاء أيقونة شريط النظام")

            # حفظ الإعدادات النهائية
            if self.settings_manager:
                try:
                    self.settings_manager.save_settings()
                    self.logger.info("تم حفظ الإعدادات")
                except Exception as e:
                    self.logger.error(f"خطأ في حفظ الإعدادات: {e}")

            # إغلاق قاعدة البيانات
            if self.db_manager:
                try:
                    # إضافة أي عمليات إغلاق خاصة بقاعدة البيانات هنا
                    self.logger.info("تم إغلاق قاعدة البيانات")
                except Exception as e:
                    self.logger.error(f"خطأ في إغلاق قاعدة البيانات: {e}")

            # تسجيل إنهاء التطبيق
            self.logger.info("تم تنظيف جميع موارد التطبيق بنجاح")
            self.logger.info("=" * 60)
            self.logger.info("SmartPSS Pro - تم الإنهاء")
            self.logger.info("=" * 60)

        except Exception as e:
            self.logger.error(f"خطأ في تنظيف الموارد: {e}")

        finally:
            # التأكد من إنهاء جميع العمليات
            try:
                # إنهاء أي عمليات متبقية
                self.processEvents()
            except:
                pass

def check_dependencies():
    """فحص التبعيات المطلوبة للتطبيق"""
    print("فحص التبعيات المطلوبة...")

    required_deps = [
        ("cv2", "opencv-python", "معالجة الفيديو وكشف الحركة"),
        ("PyQt5", "PyQt5", "واجهة المستخدم الرسومية"),
        ("numpy", "numpy", "العمليات الرياضية"),
        ("psutil", "psutil", "مراقبة النظام"),
        ("requests", "requests", "الاتصال بالشبكة")
    ]

    optional_deps = [
        ("playsound", "playsound", "تشغيل الأصوات"),
        ("schedule", "schedule", "جدولة المهام"),
        ("PIL", "Pillow", "معالجة الصور")
    ]

    missing_required = []
    missing_optional = []

    # فحص التبعيات المطلوبة
    for module, package, description in required_deps:
        try:
            __import__(module)
            print(f"✓ {package} - {description}")
        except ImportError:
            missing_required.append(package)
            print(f"✗ {package} - مفقود - {description}")

    # فحص التبعيات الاختيارية
    print("\nالتبعيات الاختيارية:")
    for module, package, description in optional_deps:
        try:
            __import__(module)
            print(f"✓ {package} - {description}")
        except ImportError:
            missing_optional.append(package)
            print(f"⚠ {package} - مفقود (اختياري) - {description}")

    if missing_required:
        print(f"\n❌ التبعيات المطلوبة المفقودة: {len(missing_required)}")
        for dep in missing_required:
            print(f"  - {dep}")
        print("\nلتثبيت التبعيات المفقودة:")
        print("pip install " + " ".join(missing_required))
        return False

    if missing_optional:
        print(f"\n⚠ التبعيات الاختيارية المفقودة: {len(missing_optional)}")
        print("بعض الميزات قد لا تعمل بشكل كامل")

    print(f"\n✅ جميع التبعيات المطلوبة متوفرة!")
    return True

def create_directories():
    """إنشاء المجلدات المطلوبة للتطبيق"""
    print("إنشاء هيكل المجلدات...")

    directories = {
        "videos": "مجلد التسجيلات",
        "logs": "مجلد ملفات السجل",
        "snapshots": "مجلد لقطات الحركة",
        "sounds": "مجلد الأصوات",
        "resources": "مجلد الموارد",
        "resources/icons": "مجلد الأيقونات",
        "resources/themes": "مجلد المظاهر",
        "translations": "مجلد ملفات الترجمة",
        "backups": "مجلد النسخ الاحتياطية",
        "crash_reports": "مجلد تقارير الأخطاء",
        "temp": "مجلد الملفات المؤقتة",
        "exports": "مجلد الملفات المصدرة"
    }

    created_count = 0
    for directory, description in directories.items():
        try:
            dir_path = Path(directory)
            if not dir_path.exists():
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"✓ تم إنشاء: {directory} - {description}")
                created_count += 1
            else:
                print(f"✓ موجود: {directory} - {description}")
        except Exception as e:
            print(f"✗ فشل في إنشاء {directory}: {e}")

    if created_count > 0:
        print(f"\nتم إنشاء {created_count} مجلد جديد")
    else:
        print("\nجميع المجلدات موجودة مسبقاً")

    # إنشاء ملفات التكوين الافتراضية
    create_default_config_files()

def create_default_config_files():
    """إنشاء ملفات التكوين الافتراضية"""
    try:
        # ملف الإعدادات الافتراضية
        default_settings = {
            "version": "2.1.0",
            "language": "ar",
            "theme": "dark",
            "first_run": True,
            "auto_update_check": True,
            "performance_monitoring": True,
            "crash_reporting": True,
            "motion_detection_enabled": True,
            "auto_save_snapshots": True,
            "minimize_to_tray": True
        }

        settings_file = Path("default_settings.json")
        if not settings_file.exists():
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(default_settings, f, ensure_ascii=False, indent=2)
            print(f"✓ تم إنشاء ملف الإعدادات الافتراضية")

        # ملف README للمستخدم
        readme_content = """# SmartPSS Pro - نظام المراقبة الذكي المتطور

## معلومات سريعة
- الإصدار: 2.1.0
- تاريخ الإنشاء: {date}
- المستخدم الافتراضي: admin
- كلمة المرور الافتراضية: admin

## المجلدات المهمة
- videos/: التسجيلات
- logs/: ملفات السجل
- snapshots/: لقطات الحركة
- backups/: النسخ الاحتياطية

## المزايا الجديدة في الإصدار 2.1.0
- كاشف حركة متطور مع تحكم فردي وجماعي
- واجهة تسجيل دخول محسنة مع تصميم عصري
- نظام إدارة ملفات متقدم مع تصفح المجلدات
- تحسينات في الأداء والاستقرار
- دعم أفضل للغة العربية

## للدعم والمساعدة
راجع ملف README.md الرئيسي للحصول على تعليمات مفصلة.
""".format(date=datetime.now().strftime('%Y-%m-%d'))

        readme_file = Path("USER_README.txt")
        if not readme_file.exists():
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            print(f"✓ تم إنشاء ملف دليل المستخدم")

    except Exception as e:
        print(f"خطأ في إنشاء ملفات التكوين: {e}")

def main():
    """نقطة دخول التطبيق الرئيسية المحسنة"""
    print("=" * 70)
    print("🚀 SmartPSS Pro - نظام المراقبة الذكي المتطور")
    print("=" * 70)
    print(f"📅 تاريخ البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {platform.python_version()}")
    print(f"💻 النظام: {platform.system()} {platform.release()}")
    print("=" * 70)

    try:
        # فحص التبعيات المطلوبة
        print("\n🔍 فحص التبعيات...")
        if not check_dependencies():
            print("\n❌ فشل في فحص التبعيات!")
            input("اضغط Enter للخروج...")
            return 1

        # إنشاء هيكل المجلدات
        print("\n📁 إعداد هيكل المجلدات...")
        create_directories()

        # إنشاء وتشغيل التطبيق
        print("\n🎯 بدء تشغيل SmartPSS Pro...")
        app = SmartPSSProApplication(sys.argv)

        # إعداد معالج الاستثناءات المتقدم
        def advanced_exception_handler(exc_type, exc_value, exc_traceback):
            if app.crash_handler:
                app.crash_handler.handle_exception(exc_type, exc_value, exc_traceback)
            else:
                app.logger.error(f"خطأ غير متوقع: {exc_type.__name__}: {exc_value}")

        sys.excepthook = advanced_exception_handler

        # تشغيل التطبيق
        print("✅ تم تهيئة التطبيق بنجاح!")
        print("🎮 بدء واجهة المستخدم...")

        exit_code = app.run()

        # تنظيف الموارد
        print("\n🧹 تنظيف الموارد...")
        app.cleanup()

        print(f"\n✅ تم إنهاء التطبيق برمز الخروج: {exit_code}")
        return exit_code

    except KeyboardInterrupt:
        print("\n\n⚠️ تم إيقاف التطبيق بواسطة المستخدم (Ctrl+C)")
        print("🔄 تنظيف الموارد...")
        try:
            if 'app' in locals():
                app.cleanup()
        except:
            pass
        return 0

    except Exception as e:
        print(f"\n💥 خطأ فادح في التطبيق: {e}")
        print("📝 تحقق من ملفات السجل للحصول على تفاصيل أكثر")

        # محاولة حفظ تقرير الخطأ
        try:
            error_report = {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'system_info': {
                    'platform': platform.system(),
                    'python_version': platform.python_version(),
                    'architecture': platform.architecture()[0]
                }
            }

            error_file = Path("crash_reports") / f"fatal_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            error_file.parent.mkdir(exist_ok=True)

            with open(error_file, 'w', encoding='utf-8') as f:
                json.dump(error_report, f, ensure_ascii=False, indent=2, default=str)

            print(f"💾 تم حفظ تقرير الخطأ في: {error_file}")

        except Exception as save_error:
            print(f"⚠️ فشل في حفظ تقرير الخطأ: {save_error}")

        input("\nاضغط Enter للخروج...")
        return 1

    finally:
        print("\n" + "=" * 70)
        print("👋 شكراً لاستخدام SmartPSS Pro")
        print("=" * 70)

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
