#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SmartPSS Pro - System Diagnosis
تشخيص شامل للنظام والمشاكل
"""

import sys
import os
import platform
import subprocess

def print_section(title):
    """طباعة عنوان قسم"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def check_python_info():
    """فحص معلومات Python"""
    print_section("معلومات Python")
    
    print(f"إصدار Python: {sys.version}")
    print(f"مسار Python: {sys.executable}")
    print(f"معمارية النظام: {platform.architecture()}")
    print(f"نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"المعالج: {platform.processor()}")
    
    # فحص مسارات Python
    print(f"\nمسارات Python:")
    for i, path in enumerate(sys.path):
        print(f"  {i+1}. {path}")

def check_pip_packages():
    """فحص الحزم المثبتة"""
    print_section("الحزم المثبتة")
    
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'list'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            relevant_packages = []
            for line in lines:
                if any(pkg in line.lower() for pkg in ['pyqt', 'qt', 'numpy', 'opencv', 'psutil']):
                    relevant_packages.append(line)
            
            if relevant_packages:
                print("الحزم ذات الصلة:")
                for pkg in relevant_packages:
                    print(f"  {pkg}")
            else:
                print("لم يتم العثور على حزم ذات صلة")
        else:
            print(f"خطأ في تشغيل pip: {result.stderr}")
    except Exception as e:
        print(f"خطأ في فحص الحزم: {e}")

def test_imports():
    """اختبار استيراد المكتبات"""
    print_section("اختبار استيراد المكتبات")
    
    libraries = {
        'PyQt5.QtWidgets': 'PyQt5 Widgets',
        'PyQt5.QtCore': 'PyQt5 Core',
        'PyQt5.QtGui': 'PyQt5 GUI',
        'numpy': 'NumPy',
        'cv2': 'OpenCV',
        'psutil': 'psutil',
        'sqlite3': 'SQLite3',
        'json': 'JSON',
        'datetime': 'DateTime'
    }
    
    for lib, name in libraries.items():
        try:
            __import__(lib)
            print(f"✅ {name}: متوفر")
        except ImportError as e:
            print(f"❌ {name}: غير متوفر - {e}")
        except Exception as e:
            print(f"⚠️ {name}: خطأ - {e}")

def test_pyqt5_detailed():
    """اختبار PyQt5 بالتفصيل"""
    print_section("اختبار PyQt5 التفصيلي")
    
    try:
        print("محاولة استيراد PyQt5...")
        import PyQt5
        print(f"✅ PyQt5 متوفر - الإصدار: {PyQt5.Qt.PYQT_VERSION_STR}")
        print(f"✅ Qt الإصدار: {PyQt5.Qt.QT_VERSION_STR}")
        
        print("\nاختبار مكونات PyQt5:")
        components = [
            ('PyQt5.QtWidgets', 'Widgets'),
            ('PyQt5.QtCore', 'Core'),
            ('PyQt5.QtGui', 'GUI'),
            ('PyQt5.sip', 'SIP')
        ]
        
        for comp, name in components:
            try:
                __import__(comp)
                print(f"  ✅ {name}")
            except ImportError as e:
                print(f"  ❌ {name}: {e}")
        
        # اختبار إنشاء تطبيق
        print("\nاختبار إنشاء QApplication...")
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("✅ QApplication تم إنشاؤه بنجاح")
        
        # اختبار إنشاء widget
        print("اختبار إنشاء QWidget...")
        from PyQt5.QtWidgets import QWidget
        widget = QWidget()
        print("✅ QWidget تم إنشاؤه بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في PyQt5: {e}")
        import traceback
        print(f"التفاصيل: {traceback.format_exc()}")
        return False

def check_file_structure():
    """فحص هيكل الملفات"""
    print_section("فحص هيكل الملفات")
    
    current_dir = os.getcwd()
    print(f"المجلد الحالي: {current_dir}")
    
    important_files = [
        'main.py',
        'simple_main.py',
        'safe_main.py',
        'minimal_test.py',
        'database.py',
        'settings.py',
        'user_manager.py',
        'requirements.txt',
        'ui/main_window.py',
        'ui/login_dialog.py'
    ]
    
    print("\nالملفات المهمة:")
    for file in important_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  ✅ {file} ({size} bytes)")
        else:
            print(f"  ❌ {file} (مفقود)")

def suggest_solutions():
    """اقتراح الحلول"""
    print_section("الحلول المقترحة")
    
    print("1. إعادة تثبيت PyQt5:")
    print("   pip uninstall PyQt5")
    print("   pip install PyQt5")
    print()
    
    print("2. تثبيت إصدار محدد من PyQt5:")
    print("   pip install PyQt5==5.15.10")
    print()
    
    print("3. استخدام conda بدلاً من pip:")
    print("   conda install pyqt")
    print()
    
    print("4. إنشاء بيئة افتراضية جديدة:")
    print("   python -m venv new_env")
    print("   new_env\\Scripts\\activate")
    print("   pip install PyQt5")
    print()
    
    print("5. تشغيل الاختبار الأساسي:")
    print("   python minimal_test.py")

def main():
    """التشخيص الرئيسي"""
    print("🔍 SmartPSS Pro - تشخيص شامل للنظام")
    print("=" * 60)
    
    # فحص معلومات Python
    check_python_info()
    
    # فحص الحزم المثبتة
    check_pip_packages()
    
    # اختبار الاستيراد
    test_imports()
    
    # اختبار PyQt5 بالتفصيل
    pyqt5_works = test_pyqt5_detailed()
    
    # فحص هيكل الملفات
    check_file_structure()
    
    # اقتراح الحلول
    suggest_solutions()
    
    # الخلاصة
    print_section("الخلاصة")
    if pyqt5_works:
        print("✅ PyQt5 يعمل بشكل صحيح")
        print("💡 جرب تشغيل: python minimal_test.py")
    else:
        print("❌ PyQt5 لا يعمل بشكل صحيح")
        print("💡 اتبع الحلول المقترحة أعلاه")
    
    print("\n" + "="*60)
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nتم إيقاف التشخيص")
    except Exception as e:
        print(f"\nخطأ في التشخيص: {e}")
        import traceback
        print(f"التفاصيل: {traceback.format_exc()}")
        input("اضغط Enter للخروج...")
