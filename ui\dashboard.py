#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Dashboard for SmartPSS Pro
لوحة معلومات متقدمة لنظام SmartPSS Pro
"""

import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                           QLabel, QFrame, QProgressBar, QPushButton, QScrollArea,
                           QGroupBox, QListWidget, QTabWidget, QTableWidget,
                           QTableWidgetItem, QHeaderView, QSplitter)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QPalette, QPixmap, QPainter
from PyQt5.QtChart import QChart, QChartView, QLineSeries, QPieSeries, QBarSeries, QBarSet
import psutil
import platform

class SystemMonitorWidget(QFrame):
    """ودجة مراقبة النظام"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_timer()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                background-color: #1E1E1E;
                border: 1px solid #333;
                border-radius: 8px;
                padding: 10px;
            }
            QLabel {
                color: white;
                font-size: 12px;
            }
            QProgressBar {
                border: 1px solid #333;
                border-radius: 4px;
                text-align: center;
                background-color: #2D2D2D;
            }
            QProgressBar::chunk {
                background-color: #2196F3;
                border-radius: 3px;
            }
        """)
        
        layout = QVBoxLayout()
        
        # عنوان القسم
        title = QLabel("مراقبة النظام")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # معلومات المعالج
        cpu_layout = QHBoxLayout()
        cpu_layout.addWidget(QLabel("المعالج:"))
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setMaximum(100)
        self.cpu_label = QLabel("0%")
        cpu_layout.addWidget(self.cpu_progress)
        cpu_layout.addWidget(self.cpu_label)
        layout.addLayout(cpu_layout)
        
        # معلومات الذاكرة
        memory_layout = QHBoxLayout()
        memory_layout.addWidget(QLabel("الذاكرة:"))
        self.memory_progress = QProgressBar()
        self.memory_progress.setMaximum(100)
        self.memory_label = QLabel("0%")
        memory_layout.addWidget(self.memory_progress)
        memory_layout.addWidget(self.memory_label)
        layout.addLayout(memory_layout)
        
        # معلومات القرص
        disk_layout = QHBoxLayout()
        disk_layout.addWidget(QLabel("القرص:"))
        self.disk_progress = QProgressBar()
        self.disk_progress.setMaximum(100)
        self.disk_label = QLabel("0%")
        disk_layout.addWidget(self.disk_progress)
        disk_layout.addWidget(self.disk_label)
        layout.addLayout(disk_layout)
        
        # معلومات الشبكة
        self.network_label = QLabel("الشبكة: 0 KB/s ↑ 0 KB/s ↓")
        layout.addWidget(self.network_label)
        
        # معلومات النظام
        system_info = QLabel(f"النظام: {platform.system()} {platform.release()}")
        system_info.setWordWrap(True)
        layout.addWidget(system_info)
        
        self.setLayout(layout)
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_system_info)
        self.timer.start(2000)  # تحديث كل ثانيتين
        
        # متغيرات لحساب سرعة الشبكة
        self.last_network_io = psutil.net_io_counters()
        self.last_update_time = datetime.now()
    
    def update_system_info(self):
        """تحديث معلومات النظام"""
        try:
            # تحديث معلومات المعالج
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_progress.setValue(int(cpu_percent))
            self.cpu_label.setText(f"{cpu_percent:.1f}%")
            
            # تحديث معلومات الذاكرة
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.memory_progress.setValue(int(memory_percent))
            self.memory_label.setText(f"{memory_percent:.1f}%")
            
            # تحديث معلومات القرص
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.disk_progress.setValue(int(disk_percent))
            self.disk_label.setText(f"{disk_percent:.1f}%")
            
            # تحديث معلومات الشبكة
            current_network_io = psutil.net_io_counters()
            current_time = datetime.now()
            
            time_delta = (current_time - self.last_update_time).total_seconds()
            if time_delta > 0:
                bytes_sent_per_sec = (current_network_io.bytes_sent - self.last_network_io.bytes_sent) / time_delta
                bytes_recv_per_sec = (current_network_io.bytes_recv - self.last_network_io.bytes_recv) / time_delta
                
                upload_speed = bytes_sent_per_sec / 1024  # KB/s
                download_speed = bytes_recv_per_sec / 1024  # KB/s
                
                self.network_label.setText(f"الشبكة: {upload_speed:.1f} KB/s ↑ {download_speed:.1f} KB/s ↓")
            
            self.last_network_io = current_network_io
            self.last_update_time = current_time
            
        except Exception as e:
            print(f"خطأ في تحديث معلومات النظام: {e}")

class CameraStatusWidget(QFrame):
    """ودجة حالة الكاميرات"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                background-color: #1E1E1E;
                border: 1px solid #333;
                border-radius: 8px;
                padding: 10px;
            }
            QLabel {
                color: white;
                font-size: 12px;
            }
            QListWidget {
                background-color: #2D2D2D;
                border: 1px solid #333;
                border-radius: 4px;
                color: white;
            }
        """)
        
        layout = QVBoxLayout()
        
        # عنوان القسم
        title = QLabel("حالة الكاميرات")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # إحصائيات سريعة
        stats_layout = QGridLayout()
        
        self.total_cameras_label = QLabel("المجموع: 0")
        self.online_cameras_label = QLabel("متصلة: 0")
        self.recording_cameras_label = QLabel("تسجل: 0")
        self.motion_cameras_label = QLabel("حركة: 0")
        
        stats_layout.addWidget(self.total_cameras_label, 0, 0)
        stats_layout.addWidget(self.online_cameras_label, 0, 1)
        stats_layout.addWidget(self.recording_cameras_label, 1, 0)
        stats_layout.addWidget(self.motion_cameras_label, 1, 1)
        
        layout.addLayout(stats_layout)
        
        # قائمة الكاميرات
        self.camera_list = QListWidget()
        layout.addWidget(self.camera_list)
        
        self.setLayout(layout)
        
        # تحديث البيانات
        self.update_camera_status()
    
    def update_camera_status(self):
        """تحديث حالة الكاميرات"""
        # هذه البيانات ستأتي من مدير الكاميرات الفعلي
        sample_cameras = [
            {"name": "كاميرا المدخل الرئيسي", "status": "متصلة", "recording": True, "motion": False},
            {"name": "كاميرا الموقف", "status": "متصلة", "recording": False, "motion": True},
            {"name": "كاميرا الحديقة", "status": "غير متصلة", "recording": False, "motion": False},
            {"name": "كاميرا المكتب", "status": "متصلة", "recording": True, "motion": False},
        ]
        
        total = len(sample_cameras)
        online = sum(1 for cam in sample_cameras if cam["status"] == "متصلة")
        recording = sum(1 for cam in sample_cameras if cam["recording"])
        motion = sum(1 for cam in sample_cameras if cam["motion"])
        
        self.total_cameras_label.setText(f"المجموع: {total}")
        self.online_cameras_label.setText(f"متصلة: {online}")
        self.recording_cameras_label.setText(f"تسجل: {recording}")
        self.motion_cameras_label.setText(f"حركة: {motion}")
        
        # تحديث القائمة
        self.camera_list.clear()
        for camera in sample_cameras:
            status_icon = "🟢" if camera["status"] == "متصلة" else "🔴"
            record_icon = "🔴" if camera["recording"] else ""
            motion_icon = "⚡" if camera["motion"] else ""
            
            item_text = f"{status_icon} {camera['name']} {record_icon} {motion_icon}"
            self.camera_list.addItem(item_text)

class RecentEventsWidget(QFrame):
    """ودجة الأحداث الأخيرة"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                background-color: #1E1E1E;
                border: 1px solid #333;
                border-radius: 8px;
                padding: 10px;
            }
            QLabel {
                color: white;
                font-size: 12px;
            }
            QListWidget {
                background-color: #2D2D2D;
                border: 1px solid #333;
                border-radius: 4px;
                color: white;
            }
        """)
        
        layout = QVBoxLayout()
        
        # عنوان القسم
        title = QLabel("الأحداث الأخيرة")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # قائمة الأحداث
        self.events_list = QListWidget()
        layout.addWidget(self.events_list)
        
        # زر مسح الأحداث
        clear_button = QPushButton("مسح الأحداث")
        clear_button.clicked.connect(self.clear_events)
        layout.addWidget(clear_button)
        
        self.setLayout(layout)
        
        # إضافة أحداث تجريبية
        self.add_sample_events()
    
    def add_sample_events(self):
        """إضافة أحداث تجريبية"""
        sample_events = [
            "🔴 تم اكتشاف حركة في كاميرا المدخل الرئيسي",
            "📹 بدأ تسجيل تلقائي - كاميرا الموقف",
            "🟢 تم استعادة الاتصال - كاميرا الحديقة",
            "⚠️ انقطع الاتصال - كاميرا المكتب",
            "👤 تم تسجيل دخول المستخدم: أحمد",
            "💾 تم حفظ النسخة الاحتياطية بنجاح",
        ]
        
        for event in sample_events:
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.events_list.addItem(f"[{timestamp}] {event}")
    
    def add_event(self, event_text):
        """إضافة حدث جديد"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.events_list.insertItem(0, f"[{timestamp}] {event_text}")
        
        # الاحتفاظ بآخر 50 حدث فقط
        while self.events_list.count() > 50:
            self.events_list.takeItem(self.events_list.count() - 1)
    
    def clear_events(self):
        """مسح جميع الأحداث"""
        self.events_list.clear()

class DashboardWidget(QWidget):
    """لوحة المعلومات الرئيسية"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # عنوان لوحة المعلومات
        title = QLabel("لوحة المعلومات - SmartPSS Pro")
        title.setFont(QFont("Arial", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: white; margin: 10px; padding: 10px;")
        layout.addWidget(title)
        
        # تخطيط شبكي للودجات
        grid_layout = QGridLayout()
        
        # ودجة مراقبة النظام
        self.system_monitor = SystemMonitorWidget()
        grid_layout.addWidget(self.system_monitor, 0, 0)
        
        # ودجة حالة الكاميرات
        self.camera_status = CameraStatusWidget()
        grid_layout.addWidget(self.camera_status, 0, 1)
        
        # ودجة الأحداث الأخيرة
        self.recent_events = RecentEventsWidget()
        grid_layout.addWidget(self.recent_events, 1, 0, 1, 2)
        
        layout.addLayout(grid_layout)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("النظام يعمل بشكل طبيعي")
        self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        
        self.uptime_label = QLabel("وقت التشغيل: 00:00:00")
        self.uptime_label.setStyleSheet("color: white;")
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.uptime_label)
        
        layout.addLayout(status_layout)
        
        self.setLayout(layout)
        
        # مؤقت تحديث وقت التشغيل
        self.start_time = datetime.now()
        self.uptime_timer = QTimer()
        self.uptime_timer.timeout.connect(self.update_uptime)
        self.uptime_timer.start(1000)  # تحديث كل ثانية
    
    def update_uptime(self):
        """تحديث وقت التشغيل"""
        uptime = datetime.now() - self.start_time
        hours, remainder = divmod(int(uptime.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        self.uptime_label.setText(f"وقت التشغيل: {hours:02d}:{minutes:02d}:{seconds:02d}")
    
    def add_event(self, event_text):
        """إضافة حدث جديد لقائمة الأحداث"""
        self.recent_events.add_event(event_text)
    
    def update_camera_count(self, total, online, recording, motion):
        """تحديث إحصائيات الكاميرات"""
        # سيتم تنفيذها عند ربطها بمدير الكاميرات الفعلي
        pass
