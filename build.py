#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build script for SmartPSS application
Creates executable file using PyInstaller
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime

def check_pyinstaller():
    """Check if PyInstaller is installed"""
    try:
        import PyInstaller
        return True
    except ImportError:
        return False

def install_pyinstaller():
    """Install PyInstaller"""
    print("Installing PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        return True
    except subprocess.CalledProcessError:
        return False

def create_spec_file():
    """Create PyInstaller spec file"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ui', 'ui'),
        ('resources', 'resources'),
        ('sounds', 'sounds'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'cv2',
        'numpy',
        'sqlite3',
        'playsound',
        'schedule',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SmartPSS',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/app_icon.ico' if os.path.exists('resources/app_icon.ico') else None,
)
'''
    
    with open('SmartPSS.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("Created SmartPSS.spec file")

def build_executable():
    """Build executable using PyInstaller"""
    print("Building executable...")
    
    try:
        # Build using spec file
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm", 
            "SmartPSS.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Build successful!")
            return True
        else:
            print("✗ Build failed!")
            print("Error output:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"Build error: {e}")
        return False

def create_portable_package():
    """Create portable package with necessary files"""
    print("Creating portable package...")
    
    try:
        # Create package directory
        package_dir = "SmartPSS_Portable"
        if os.path.exists(package_dir):
            shutil.rmtree(package_dir)
        os.makedirs(package_dir)
        
        # Copy executable
        exe_path = os.path.join("dist", "SmartPSS.exe")
        if os.path.exists(exe_path):
            shutil.copy2(exe_path, package_dir)
        else:
            print("Executable not found!")
            return False
        
        # Create necessary directories
        dirs_to_create = ["videos", "logs", "snapshots", "sounds"]
        for dir_name in dirs_to_create:
            os.makedirs(os.path.join(package_dir, dir_name), exist_ok=True)
        
        # Copy README and requirements
        files_to_copy = ["README.md", "requirements.txt"]
        for file_name in files_to_copy:
            if os.path.exists(file_name):
                shutil.copy2(file_name, package_dir)
        
        # Create run script
        run_script = """@echo off
echo Starting SmartPSS...
SmartPSS.exe
if errorlevel 1 (
    echo.
    echo An error occurred. Press any key to exit.
    pause >nul
)
"""
        with open(os.path.join(package_dir, "run.bat"), 'w') as f:
            f.write(run_script)
        
        print(f"✓ Portable package created: {package_dir}")
        return True
        
    except Exception as e:
        print(f"Package creation error: {e}")
        return False

def cleanup():
    """Clean up build files"""
    print("Cleaning up...")
    
    cleanup_items = [
        "build",
        "__pycache__",
        "SmartPSS.spec",
        "dist"  # Keep dist for now, remove if needed
    ]
    
    for item in cleanup_items:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.rmtree(item)
            else:
                os.remove(item)
            print(f"Removed: {item}")

def main():
    """Main build process"""
    print("=" * 50)
    print("SmartPSS Build Script")
    print("=" * 50)
    print(f"Build started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check PyInstaller
    if not check_pyinstaller():
        print("PyInstaller not found. Installing...")
        if not install_pyinstaller():
            print("Failed to install PyInstaller!")
            return 1
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    if not build_executable():
        print("Build failed!")
        return 1
    
    # Create portable package
    if not create_portable_package():
        print("Package creation failed!")
        return 1
    
    # Optional cleanup
    response = input("\nClean up build files? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        cleanup()
    
    print()
    print("=" * 50)
    print("Build completed successfully!")
    print("Executable location: SmartPSS_Portable/SmartPSS.exe")
    print("=" * 50)
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nBuild interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)
