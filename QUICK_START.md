# 🚀 SmartPSS Pro - دليل البدء السريع

## 📋 المتطلبات الأساسية

### 1. Python
- Python 3.8 أو أحدث
- تحميل من: https://python.org

### 2. المكتبات الأساسية
```bash
pip install PyQt5 numpy psutil
```

## 🎯 التشغيل السريع

### الطريقة الأولى: ملف التشغيل (Windows)
```bash
run_smartpss.bat
```

### الطريقة الثانية: وضع الاختبار
```bash
python simple_main.py
```

### الطريقة الثالثة: النظام الكامل
```bash
python main.py
```

## 🔧 حل المشاكل الشائعة

### مشكلة: "PyQt5 غير متوفر"
```bash
pip install PyQt5
```

### مشكلة: "opencv-python غير متوفر"
```bash
pip install opencv-python
```

### مشكلة: "psutil غير متوفر"
```bash
pip install psutil
```

### تثبيت جميع المتطلبات دفعة واحدة:
```bash
pip install -r requirements.txt
```

## 📁 هيكل المشروع

```
SmartPSS-Pro/
├── simple_main.py          # نقطة دخول مبسطة للاختبار
├── main.py                 # نقطة دخول النظام الكامل
├── run_smartpss.bat        # ملف تشغيل Windows
├── requirements.txt        # متطلبات النظام
├── database.py            # إدارة قاعدة البيانات
├── settings.py            # إدارة الإعدادات
├── user_manager.py        # إدارة المستخدمين
├── ui/                    # ملفات الواجهة
│   ├── main_window.py     # النافذة الرئيسية
│   ├── login_dialog.py    # نافذة تسجيل الدخول
│   └── dashboard.py       # لوحة المعلومات
└── videos/                # مجلد التسجيلات
```

## 🎮 كيفية الاستخدام

### 1. التشغيل لأول مرة
- شغل `simple_main.py` للاختبار
- أو استخدم `run_smartpss.bat` واختر الوضع 1

### 2. تسجيل الدخول الافتراضي
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 3. اختبار المكونات
- اضغط على "اختبار قاعدة البيانات"
- اضغط على "اختبار الإعدادات"
- اضغط على "اختبار الواجهة الكاملة"

## 🔍 التحقق من التثبيت

### اختبار Python:
```bash
python --version
```

### اختبار PyQt5:
```bash
python -c "import PyQt5; print('PyQt5 OK')"
```

### اختبار OpenCV:
```bash
python -c "import cv2; print('OpenCV OK')"
```

### اختبار جميع المكتبات:
```bash
python -c "import PyQt5, cv2, numpy, psutil; print('All OK')"
```

## 📞 الدعم

### إذا واجهت مشاكل:
1. تأكد من تثبيت Python 3.8+
2. تأكد من تثبيت PyQt5
3. شغل وضع الاختبار أولاً
4. راجع ملف logs/ للأخطاء

### رسائل خطأ شائعة:

**"ModuleNotFoundError: No module named 'PyQt5'"**
```bash
pip install PyQt5
```

**"ModuleNotFoundError: No module named 'cv2'"**
```bash
pip install opencv-python
```

**"ImportError: DLL load failed"**
- أعد تثبيت PyQt5:
```bash
pip uninstall PyQt5
pip install PyQt5
```

## 🎯 الخطوات التالية

1. **اختبر النظام البسيط** - `simple_main.py`
2. **اختبر المكونات** - استخدم أزرار الاختبار
3. **شغل النظام الكامل** - `main.py`
4. **أضف كاميرات** - من خلال الواجهة
5. **استكشف الميزات** - التسجيل، كشف الحركة، إلخ

## 🔧 إعدادات متقدمة

### إنشاء بيئة افتراضية (اختياري):
```bash
python -m venv smartpss_env
smartpss_env\Scripts\activate  # Windows
pip install -r requirements.txt
```

### تشغيل النظام:
```bash
python simple_main.py  # للاختبار
python main.py         # للنظام الكامل
```

---

**SmartPSS Pro** - نظام مراقبة ذكي ومتطور 🎥✨
