{"timestamp": "2025-06-16T16:17:35.030984", "error_type": "MemoryError", "error_message": "Unable to allocate 2.64 MiB for an array with shape (720, 1280, 3) and data type uint8", "traceback": ["Traceback (most recent call last):\n", "  File \"E:\\Visual Studio Code\\073\\ui\\main_window.py\", line 740, in <lambda>\n    stream.frame_ready.connect(lambda frame, cid=camera_id: detector.update_frame(frame))\n                                                            ~~~~~~~~~~~~~~~~~~~~~^^^^^^^\n", "  File \"E:\\Visual Studio Code\\073\\motion_detector.py\", line 64, in update_frame\n    self.current_frame = frame.copy()\n                         ~~~~~~~~~~^^\n", "numpy._core._exceptions._ArrayMemoryError: Unable to allocate 2.64 MiB for an array with shape (720, 1280, 3) and data type uint8\n"], "system_info": {"cpu_percent": 29.0, "memory_percent": 91.1, "disk_usage": 15.7, "running_processes": 239, "uptime": "3:16:18.786108"}}