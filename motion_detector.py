#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Motion detection module for SmartPSS-like monitoring system
Uses OpenCV for background subtraction and motion analysis
"""

import cv2
import numpy as np
import threading
import time
from datetime import datetime
from PyQt5.QtCore import QThread, pyqtSignal
from database import DatabaseManager

class MotionDetector(QThread):
    """Motion detection thread for a single camera"""
    
    motion_detected = pyqtSignal(int, float, np.ndarray)  # camera_id, confidence, frame
    motion_ended = pyqtSignal(int)  # camera_id
    
    def __init__(self, camera_id, sensitivity="medium"):
        super().__init__()
        self.camera_id = camera_id
        self.sensitivity = sensitivity
        self.running = False
        self.current_frame = None
        self.background_subtractor = None
        self.motion_active = False
        self.motion_start_time = None
        self.motion_threshold = self.get_sensitivity_threshold(sensitivity)
        self.min_contour_area = 500
        self.motion_cooldown = 2  # seconds
        self.last_motion_time = 0
        
        # Initialize background subtractor
        self.init_background_subtractor()
    
    def init_background_subtractor(self):
        """Initialize background subtraction algorithm"""
        # Using MOG2 background subtractor
        self.background_subtractor = cv2.createBackgroundSubtractorMOG2(
            detectShadows=True,
            varThreshold=16,
            history=500
        )
    
    def get_sensitivity_threshold(self, sensitivity):
        """Get motion detection threshold based on sensitivity"""
        thresholds = {
            "low": 0.02,
            "medium": 0.01,
            "high": 0.005
        }
        return thresholds.get(sensitivity, 0.01)
    
    def set_sensitivity(self, sensitivity):
        """Update motion detection sensitivity"""
        self.sensitivity = sensitivity
        self.motion_threshold = self.get_sensitivity_threshold(sensitivity)
    
    def update_frame(self, frame):
        """Update current frame for motion detection"""
        self.current_frame = frame.copy()
    
    def run(self):
        """Main motion detection loop"""
        self.running = True
        
        while self.running:
            if self.current_frame is not None:
                try:
                    motion_detected, confidence = self.detect_motion(self.current_frame)
                    current_time = time.time()
                    
                    if motion_detected and (current_time - self.last_motion_time) > self.motion_cooldown:
                        if not self.motion_active:
                            self.motion_active = True
                            self.motion_start_time = datetime.now()
                        
                        self.motion_detected.emit(self.camera_id, confidence, self.current_frame)
                        self.last_motion_time = current_time
                        
                    elif self.motion_active and not motion_detected:
                        # Check if motion has ended (no motion for a while)
                        if (current_time - self.last_motion_time) > self.motion_cooldown * 2:
                            self.motion_active = False
                            self.motion_ended.emit(self.camera_id)
                            
                except Exception as e:
                    print(f"Motion detection error for camera {self.camera_id}: {e}")
            
            time.sleep(0.1)  # Check every 100ms
    
    def detect_motion(self, frame):
        """Detect motion in the current frame"""
        if frame is None:
            return False, 0.0
        
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (21, 21), 0)
            
            # Apply background subtraction
            fg_mask = self.background_subtractor.apply(blurred)
            
            # Remove shadows
            fg_mask[fg_mask == 127] = 0
            
            # Apply morphological operations to clean up the mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter contours by area
            significant_contours = [c for c in contours if cv2.contourArea(c) > self.min_contour_area]
            
            if significant_contours:
                # Calculate motion confidence based on total motion area
                total_motion_area = sum(cv2.contourArea(c) for c in significant_contours)
                frame_area = frame.shape[0] * frame.shape[1]
                motion_ratio = total_motion_area / frame_area
                
                # Check if motion exceeds threshold
                if motion_ratio > self.motion_threshold:
                    confidence = min(motion_ratio * 100, 100.0)  # Convert to percentage, cap at 100%
                    return True, confidence
            
            return False, 0.0
            
        except Exception as e:
            print(f"Motion detection processing error: {e}")
            return False, 0.0
    
    def get_motion_mask(self, frame):
        """Get motion detection mask for visualization"""
        if frame is None:
            return None
        
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            blurred = cv2.GaussianBlur(gray, (21, 21), 0)
            fg_mask = self.background_subtractor.apply(blurred)
            
            # Clean up mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_OPEN, kernel)
            fg_mask = cv2.morphologyEx(fg_mask, cv2.MORPH_CLOSE, kernel)
            
            return fg_mask
            
        except Exception:
            return None
    
    def draw_motion_overlay(self, frame):
        """Draw motion detection overlay on frame"""
        if frame is None:
            return frame
        
        try:
            overlay_frame = frame.copy()
            fg_mask = self.get_motion_mask(frame)
            
            if fg_mask is not None:
                # Find contours
                contours, _ = cv2.findContours(fg_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                # Draw bounding rectangles around motion areas
                for contour in contours:
                    if cv2.contourArea(contour) > self.min_contour_area:
                        x, y, w, h = cv2.boundingRect(contour)
                        cv2.rectangle(overlay_frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                        
                        # Add motion label
                        cv2.putText(overlay_frame, "MOTION", (x, y - 10),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            return overlay_frame
            
        except Exception:
            return frame
    
    def stop(self):
        """Stop motion detection"""
        self.running = False
        self.wait()

class MotionManager:
    """Manages motion detection for multiple cameras"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.detectors = {}
        self.motion_callbacks = []
    
    def start_detection(self, camera_id, sensitivity="medium"):
        """Start motion detection for a camera"""
        if camera_id not in self.detectors:
            detector = MotionDetector(camera_id, sensitivity)
            detector.motion_detected.connect(self.on_motion_detected)
            detector.motion_ended.connect(self.on_motion_ended)
            
            self.detectors[camera_id] = detector
            detector.start()
    
    def stop_detection(self, camera_id):
        """Stop motion detection for a camera"""
        if camera_id in self.detectors:
            self.detectors[camera_id].stop()
            del self.detectors[camera_id]
    
    def stop_all_detection(self):
        """Stop all motion detection"""
        for camera_id in list(self.detectors.keys()):
            self.stop_detection(camera_id)
    
    def update_frame(self, camera_id, frame):
        """Update frame for motion detection"""
        if camera_id in self.detectors:
            self.detectors[camera_id].update_frame(frame)
    
    def set_sensitivity(self, camera_id, sensitivity):
        """Update sensitivity for a camera"""
        if camera_id in self.detectors:
            self.detectors[camera_id].set_sensitivity(sensitivity)
    
    def on_motion_detected(self, camera_id, confidence, frame):
        """Handle motion detection event"""
        # Log motion event to database
        self.db.add_motion_event(camera_id, confidence)
        
        # Save snapshot if configured
        snapshot_path = self.save_motion_snapshot(camera_id, frame)
        
        # Notify callbacks
        for callback in self.motion_callbacks:
            try:
                callback(camera_id, confidence, frame, snapshot_path)
            except Exception as e:
                print(f"Motion callback error: {e}")
    
    def on_motion_ended(self, camera_id):
        """Handle motion end event"""
        print(f"Motion ended for camera {camera_id}")
    
    def save_motion_snapshot(self, camera_id, frame):
        """Save motion detection snapshot"""
        try:
            import os
            from datetime import datetime
            
            # Create snapshots directory
            snapshots_dir = "snapshots"
            if not os.path.exists(snapshots_dir):
                os.makedirs(snapshots_dir)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"motion_cam{camera_id}_{timestamp}.jpg"
            filepath = os.path.join(snapshots_dir, filename)
            
            # Save frame
            cv2.imwrite(filepath, frame)
            return filepath
            
        except Exception as e:
            print(f"Failed to save motion snapshot: {e}")
            return None
    
    def add_motion_callback(self, callback):
        """Add callback for motion events"""
        self.motion_callbacks.append(callback)
    
    def remove_motion_callback(self, callback):
        """Remove motion callback"""
        if callback in self.motion_callbacks:
            self.motion_callbacks.remove(callback)
    
    def get_detector(self, camera_id):
        """Get motion detector for camera"""
        return self.detectors.get(camera_id)
    
    def is_detecting(self, camera_id):
        """Check if motion detection is active for camera"""
        return camera_id in self.detectors
