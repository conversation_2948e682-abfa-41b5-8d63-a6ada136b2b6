#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Settings management module for SmartPSS-like monitoring system
Handles application configuration and preferences
"""

import json
import os
from datetime import datetime
from database import DatabaseManager

class SettingsManager:
    """Manages application settings and configuration"""
    
    def __init__(self, settings_file="settings.json"):
        self.settings_file = settings_file
        self.db = DatabaseManager()
        self.settings_cache = {}
        
        # Default settings with Arabic labels
        self.default_settings = {
            # Language and Localization
            'language': {
                'value': 'ar',
                'type': 'select',
                'options': ['ar', 'en'],
                'label': 'اللغة',  # Language
                'description': 'لغة واجهة التطبيق'  # Application interface language
            },
            
            # Theme and Appearance
            'theme': {
                'value': 'dark',
                'type': 'select',
                'options': ['dark', 'light'],
                'label': 'المظهر',  # Theme
                'description': 'مظهر التطبيق'  # Application theme
            },
            'window_opacity': {
                'value': 1.0,
                'type': 'float',
                'min': 0.5,
                'max': 1.0,
                'label': 'شفافية النافذة',  # Window opacity
                'description': 'مستوى شفافية النافذة الرئيسية'
            },
            
            # Video and Display Settings
            'video_quality': {
                'value': 'high',
                'type': 'select',
                'options': ['low', 'medium', 'high'],
                'label': 'جودة الفيديو',  # Video quality
                'description': 'جودة عرض الفيديو المباشر'
            },
            'fps_limit': {
                'value': 30,
                'type': 'int',
                'min': 15,
                'max': 60,
                'label': 'حد الإطارات في الثانية',  # FPS limit
                'description': 'الحد الأقصى لعدد الإطارات في الثانية'
            },
            'grid_layout': {
                'value': '2x2',
                'type': 'select',
                'options': ['1x1', '2x2', '3x3', '4x4'],
                'label': 'تخطيط الشبكة',  # Grid layout
                'description': 'تخطيط عرض الكاميرات'
            },
            
            # Recording Settings
            'recording_quality': {
                'value': 'high',
                'type': 'select',
                'options': ['low', 'medium', 'high'],
                'label': 'جودة التسجيل',  # Recording quality
                'description': 'جودة تسجيل الفيديو'
            },
            'recording_format': {
                'value': 'mp4',
                'type': 'select',
                'options': ['mp4', 'avi', 'mkv'],
                'label': 'تنسيق التسجيل',  # Recording format
                'description': 'تنسيق ملفات الفيديو المسجلة'
            },
            'auto_recording_duration': {
                'value': 300,  # 5 minutes
                'type': 'int',
                'min': 60,
                'max': 3600,
                'label': 'مدة التسجيل التلقائي (ثانية)',  # Auto recording duration
                'description': 'مدة التسجيل التلقائي عند اكتشاف الحركة'
            },
            'storage_path': {
                'value': './videos',
                'type': 'path',
                'label': 'مسار التخزين',  # Storage path
                'description': 'مسار حفظ ملفات الفيديو'
            },
            
            # Motion Detection Settings
            'motion_sensitivity': {
                'value': 'medium',
                'type': 'select',
                'options': ['low', 'medium', 'high'],
                'label': 'حساسية كشف الحركة',  # Motion sensitivity
                'description': 'مستوى حساسية كشف الحركة'
            },
            'motion_detection_enabled': {
                'value': True,
                'type': 'bool',
                'label': 'تفعيل كشف الحركة',  # Enable motion detection
                'description': 'تفعيل أو إلغاء كشف الحركة'
            },
            'motion_recording_enabled': {
                'value': True,
                'type': 'bool',
                'label': 'تسجيل عند الحركة',  # Motion recording
                'description': 'بدء التسجيل تلقائياً عند كشف الحركة'
            },
            
            # Alert Settings
            'alert_sound_enabled': {
                'value': True,
                'type': 'bool',
                'label': 'تفعيل صوت التنبيه',  # Enable alert sound
                'description': 'تشغيل صوت عند كشف الحركة'
            },
            'alert_sound_file': {
                'value': 'default',
                'type': 'file',
                'filter': 'Audio Files (*.wav *.mp3)',
                'label': 'ملف صوت التنبيه',  # Alert sound file
                'description': 'ملف الصوت المستخدم للتنبيهات'
            },
            'email_alerts_enabled': {
                'value': False,
                'type': 'bool',
                'label': 'تنبيهات البريد الإلكتروني',  # Email alerts
                'description': 'إرسال تنبيهات عبر البريد الإلكتروني'
            },
            'email_address': {
                'value': '',
                'type': 'string',
                'label': 'عنوان البريد الإلكتروني',  # Email address
                'description': 'عنوان البريد لإرسال التنبيهات'
            },
            
            # System Settings
            'auto_cleanup_enabled': {
                'value': True,
                'type': 'bool',
                'label': 'تنظيف تلقائي',  # Auto cleanup
                'description': 'حذف الملفات القديمة تلقائياً'
            },
            'auto_cleanup_days': {
                'value': 30,
                'type': 'int',
                'min': 1,
                'max': 365,
                'label': 'أيام التنظيف التلقائي',  # Auto cleanup days
                'description': 'عدد الأيام قبل حذف الملفات القديمة'
            },
            'startup_with_system': {
                'value': False,
                'type': 'bool',
                'label': 'بدء مع النظام',  # Start with system
                'description': 'بدء التطبيق مع بدء تشغيل النظام'
            },
            'minimize_to_tray': {
                'value': True,
                'type': 'bool',
                'label': 'تصغير إلى الشريط',  # Minimize to tray
                'description': 'تصغير التطبيق إلى شريط النظام'
            },
            
            # Network Settings
            'rtsp_timeout': {
                'value': 10,
                'type': 'int',
                'min': 5,
                'max': 60,
                'label': 'مهلة اتصال RTSP (ثانية)',  # RTSP timeout
                'description': 'مهلة انتظار اتصال الكاميرا'
            },
            'reconnect_attempts': {
                'value': 5,
                'type': 'int',
                'min': 1,
                'max': 20,
                'label': 'محاولات إعادة الاتصال',  # Reconnect attempts
                'description': 'عدد محاولات إعادة الاتصال بالكاميرا'
            },
            'buffer_size': {
                'value': 1,
                'type': 'int',
                'min': 1,
                'max': 10,
                'label': 'حجم المخزن المؤقت',  # Buffer size
                'description': 'حجم المخزن المؤقت للفيديو'
            }
        }
        
        self.load_settings()
    
    def load_settings(self):
        """Load settings from database and file"""
        try:
            # Load from database first
            for key in self.default_settings.keys():
                db_value = self.db.get_setting(key)
                if db_value is not None:
                    self.settings_cache[key] = self.parse_setting_value(db_value, key)
                else:
                    self.settings_cache[key] = self.default_settings[key]['value']
            
            # Load from JSON file if exists
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    file_settings = json.load(f)
                    self.settings_cache.update(file_settings)
            
        except Exception as e:
            print(f"Error loading settings: {e}")
            # Use default settings
            self.settings_cache = {key: info['value'] for key, info in self.default_settings.items()}
    
    def save_settings(self):
        """Save settings to database and file"""
        try:
            # Save to database
            for key, value in self.settings_cache.items():
                self.db.set_setting(key, str(value))
            
            # Save to JSON file
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings_cache, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"Error saving settings: {e}")
            return False
    
    def get_setting(self, key, default=None):
        """Get setting value"""
        if key in self.settings_cache:
            return self.settings_cache[key]
        elif key in self.default_settings:
            return self.default_settings[key]['value']
        else:
            return default
    
    def set_setting(self, key, value):
        """Set setting value"""
        if key in self.default_settings:
            # Validate value
            if self.validate_setting_value(key, value):
                self.settings_cache[key] = value
                return True
            else:
                return False
        else:
            # Allow custom settings
            self.settings_cache[key] = value
            return True
    
    def validate_setting_value(self, key, value):
        """Validate setting value against constraints"""
        if key not in self.default_settings:
            return True  # Allow custom settings
        
        setting_info = self.default_settings[key]
        setting_type = setting_info['type']
        
        try:
            if setting_type == 'bool':
                return isinstance(value, bool)
            elif setting_type == 'int':
                if not isinstance(value, int):
                    return False
                min_val = setting_info.get('min')
                max_val = setting_info.get('max')
                if min_val is not None and value < min_val:
                    return False
                if max_val is not None and value > max_val:
                    return False
                return True
            elif setting_type == 'float':
                if not isinstance(value, (int, float)):
                    return False
                min_val = setting_info.get('min')
                max_val = setting_info.get('max')
                if min_val is not None and value < min_val:
                    return False
                if max_val is not None and value > max_val:
                    return False
                return True
            elif setting_type == 'select':
                return value in setting_info.get('options', [])
            elif setting_type in ['string', 'path', 'file']:
                return isinstance(value, str)
            else:
                return True
                
        except Exception:
            return False
    
    def parse_setting_value(self, value_str, key):
        """Parse setting value from string"""
        if key not in self.default_settings:
            return value_str
        
        setting_type = self.default_settings[key]['type']
        
        try:
            if setting_type == 'bool':
                return value_str.lower() in ['true', '1', 'yes', 'on']
            elif setting_type == 'int':
                return int(value_str)
            elif setting_type == 'float':
                return float(value_str)
            else:
                return value_str
        except Exception:
            return self.default_settings[key]['value']
    
    def get_setting_info(self, key):
        """Get setting metadata"""
        return self.default_settings.get(key, {})
    
    def get_all_settings(self):
        """Get all settings with metadata"""
        result = {}
        for key, info in self.default_settings.items():
            result[key] = {
                'value': self.get_setting(key),
                'info': info
            }
        return result
    
    def reset_setting(self, key):
        """Reset setting to default value"""
        if key in self.default_settings:
            self.settings_cache[key] = self.default_settings[key]['value']
            return True
        return False
    
    def reset_all_settings(self):
        """Reset all settings to default values"""
        self.settings_cache = {key: info['value'] for key, info in self.default_settings.items()}
    
    def export_settings(self, file_path):
        """Export settings to file"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.settings_cache, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"Error exporting settings: {e}")
            return False
    
    def import_settings(self, file_path):
        """Import settings from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # Validate and apply settings
            for key, value in imported_settings.items():
                if self.validate_setting_value(key, value):
                    self.settings_cache[key] = value
            
            return True
        except Exception as e:
            print(f"Error importing settings: {e}")
            return False
    
    def get_categories(self):
        """Get settings organized by categories"""
        categories = {
            'عام': ['language', 'theme', 'window_opacity'],  # General
            'الفيديو': ['video_quality', 'fps_limit', 'grid_layout'],  # Video
            'التسجيل': ['recording_quality', 'recording_format', 'auto_recording_duration', 'storage_path'],  # Recording
            'كشف الحركة': ['motion_sensitivity', 'motion_detection_enabled', 'motion_recording_enabled'],  # Motion Detection
            'التنبيهات': ['alert_sound_enabled', 'alert_sound_file', 'email_alerts_enabled', 'email_address'],  # Alerts
            'النظام': ['auto_cleanup_enabled', 'auto_cleanup_days', 'startup_with_system', 'minimize_to_tray'],  # System
            'الشبكة': ['rtsp_timeout', 'reconnect_attempts', 'buffer_size']  # Network
        }
        
        result = {}
        for category, keys in categories.items():
            result[category] = {}
            for key in keys:
                if key in self.default_settings:
                    result[category][key] = {
                        'value': self.get_setting(key),
                        'info': self.default_settings[key]
                    }
        
        return result
    
    def apply_theme_settings(self):
        """Apply theme-related settings"""
        theme = self.get_setting('theme')
        opacity = self.get_setting('window_opacity')
        
        return {
            'theme': theme,
            'opacity': opacity,
            'style_sheet': self.get_theme_stylesheet(theme)
        }
    
    def get_theme_stylesheet(self, theme):
        """Get CSS stylesheet for theme"""
        if theme == 'dark':
            return """
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #353535;
            }
            QLineEdit, QTextEdit {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 3px;
                border-radius: 3px;
            }
            QComboBox {
                background-color: #404040;
                border: 1px solid #555555;
                padding: 3px;
                border-radius: 3px;
            }
            QListWidget, QTreeWidget {
                background-color: #353535;
                border: 1px solid #555555;
            }
            QMenuBar {
                background-color: #2b2b2b;
                border-bottom: 1px solid #555555;
            }
            QMenuBar::item:selected {
                background-color: #404040;
            }
            QStatusBar {
                background-color: #2b2b2b;
                border-top: 1px solid #555555;
            }
            """
        else:  # light theme
            return """
            QMainWindow {
                background-color: #ffffff;
                color: #000000;
            }
            QWidget {
                background-color: #ffffff;
                color: #000000;
            }
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            """
