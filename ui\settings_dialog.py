#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Settings Dialog for SmartPSS Pro
نافذة الإعدادات
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                           QLineEdit, QComboBox, QSpinBox, QPushButton, 
                           QLabel, QGroupBox, QCheckBox, QTabWidget,
                           QWidget, QMessageBox, QSlider, QFileDialog,
                           QTextEdit, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
import os

class SettingsDialog(QDialog):
    """نافذة إعدادات النظام"""
    
    settings_changed = pyqtSignal()
    
    def __init__(self, settings_manager, parent=None):
        super().__init__(parent)
        self.settings_manager = settings_manager
        self.setWindowTitle("إعدادات SmartPSS Pro")
        self.setModal(True)
        self.resize(600, 500)
        self.setup_ui()
        self.load_settings()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # تبويبات الإعدادات
        tabs = QTabWidget()
        
        # تبويب الإعدادات العامة
        general_tab = self.create_general_tab()
        tabs.addTab(general_tab, "عام")
        
        # تبويب إعدادات الفيديو
        video_tab = self.create_video_tab()
        tabs.addTab(video_tab, "الفيديو")
        
        # تبويب إعدادات التسجيل
        recording_tab = self.create_recording_tab()
        tabs.addTab(recording_tab, "التسجيل")
        
        # تبويب إعدادات التنبيهات
        alerts_tab = self.create_alerts_tab()
        tabs.addTab(alerts_tab, "التنبيهات")
        
        # تبويب إعدادات النظام
        system_tab = self.create_system_tab()
        tabs.addTab(system_tab, "النظام")
        
        layout.addWidget(tabs)
        
        # أزرار التحكم
        button_layout = QHBoxLayout()
        
        self.apply_button = QPushButton("تطبيق")
        self.apply_button.clicked.connect(self.apply_settings)
        
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.save_settings)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        
        self.reset_button = QPushButton("إعادة تعيين")
        self.reset_button.clicked.connect(self.reset_settings)
        
        button_layout.addWidget(self.reset_button)
        button_layout.addStretch()
        button_layout.addWidget(self.apply_button)
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        self.apply_style()
    
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات اللغة والمظهر
        appearance_group = QGroupBox("المظهر واللغة")
        appearance_layout = QFormLayout()
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        appearance_layout.addRow("اللغة:", self.language_combo)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["مظلم", "فاتح", "تلقائي"])
        appearance_layout.addRow("المظهر:", self.theme_combo)
        
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setRange(50, 100)
        self.opacity_slider.setValue(100)
        self.opacity_label = QLabel("100%")
        self.opacity_slider.valueChanged.connect(
            lambda v: self.opacity_label.setText(f"{v}%")
        )
        
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        
        appearance_layout.addRow("شفافية النافذة:", opacity_layout)
        
        appearance_group.setLayout(appearance_layout)
        layout.addWidget(appearance_group)
        
        # إعدادات بدء التشغيل
        startup_group = QGroupBox("بدء التشغيل")
        startup_layout = QVBoxLayout()
        
        self.auto_start_check = QCheckBox("بدء تلقائي مع النظام")
        self.minimize_tray_check = QCheckBox("تصغير إلى شريط النظام")
        self.minimize_tray_check.setChecked(True)
        self.remember_window_check = QCheckBox("تذكر حجم وموقع النافذة")
        self.remember_window_check.setChecked(True)
        
        startup_layout.addWidget(self.auto_start_check)
        startup_layout.addWidget(self.minimize_tray_check)
        startup_layout.addWidget(self.remember_window_check)
        
        startup_group.setLayout(startup_layout)
        layout.addWidget(startup_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        return tab
    
    def create_video_tab(self):
        """إنشاء تبويب إعدادات الفيديو"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات العرض
        display_group = QGroupBox("إعدادات العرض")
        display_layout = QFormLayout()
        
        self.default_resolution_combo = QComboBox()
        self.default_resolution_combo.addItems([
            "1920x1080", "1280x720", "640x480", "320x240"
        ])
        display_layout.addRow("الدقة الافتراضية:", self.default_resolution_combo)
        
        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(1, 60)
        self.fps_spin.setValue(25)
        display_layout.addRow("معدل الإطارات:", self.fps_spin)
        
        self.buffer_size_spin = QSpinBox()
        self.buffer_size_spin.setRange(1, 100)
        self.buffer_size_spin.setValue(10)
        display_layout.addRow("حجم المخزن المؤقت:", self.buffer_size_spin)
        
        display_group.setLayout(display_layout)
        layout.addWidget(display_group)
        
        # إعدادات الأداء
        performance_group = QGroupBox("إعدادات الأداء")
        performance_layout = QVBoxLayout()
        
        self.hardware_acceleration_check = QCheckBox("تسريع الأجهزة")
        self.hardware_acceleration_check.setChecked(True)
        
        self.multi_threading_check = QCheckBox("معالجة متعددة الخيوط")
        self.multi_threading_check.setChecked(True)
        
        self.gpu_decode_check = QCheckBox("فك التشفير بواسطة GPU")
        
        performance_layout.addWidget(self.hardware_acceleration_check)
        performance_layout.addWidget(self.multi_threading_check)
        performance_layout.addWidget(self.gpu_decode_check)
        
        performance_group.setLayout(performance_layout)
        layout.addWidget(performance_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        return tab
    
    def create_recording_tab(self):
        """إنشاء تبويب إعدادات التسجيل"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجلد التسجيل
        path_group = QGroupBox("مسارات التسجيل")
        path_layout = QFormLayout()
        
        self.video_path_edit = QLineEdit()
        self.video_path_edit.setText("videos/")
        video_path_layout = QHBoxLayout()
        video_path_layout.addWidget(self.video_path_edit)
        browse_video_btn = QPushButton("تصفح")
        browse_video_btn.clicked.connect(self.browse_video_path)
        video_path_layout.addWidget(browse_video_btn)
        path_layout.addRow("مجلد الفيديو:", video_path_layout)
        
        self.snapshot_path_edit = QLineEdit()
        self.snapshot_path_edit.setText("snapshots/")
        snapshot_path_layout = QHBoxLayout()
        snapshot_path_layout.addWidget(self.snapshot_path_edit)
        browse_snapshot_btn = QPushButton("تصفح")
        browse_snapshot_btn.clicked.connect(self.browse_snapshot_path)
        snapshot_path_layout.addWidget(browse_snapshot_btn)
        path_layout.addRow("مجلد اللقطات:", snapshot_path_layout)
        
        path_group.setLayout(path_layout)
        layout.addWidget(path_group)
        
        # إعدادات التسجيل
        recording_group = QGroupBox("إعدادات التسجيل")
        recording_layout = QFormLayout()
        
        self.auto_record_check = QCheckBox("تسجيل تلقائي عند كشف الحركة")
        self.auto_record_check.setChecked(True)
        
        self.record_duration_spin = QSpinBox()
        self.record_duration_spin.setRange(10, 3600)
        self.record_duration_spin.setValue(300)
        self.record_duration_spin.setSuffix(" ثانية")
        recording_layout.addRow("مدة التسجيل:", self.record_duration_spin)
        
        self.max_file_size_spin = QSpinBox()
        self.max_file_size_spin.setRange(100, 10000)
        self.max_file_size_spin.setValue(1000)
        self.max_file_size_spin.setSuffix(" MB")
        recording_layout.addRow("حجم الملف الأقصى:", self.max_file_size_spin)
        
        recording_group.setLayout(recording_layout)
        layout.addWidget(recording_group)
        layout.addWidget(self.auto_record_check)
        
        # إعدادات التنظيف
        cleanup_group = QGroupBox("تنظيف الملفات")
        cleanup_layout = QFormLayout()
        
        self.auto_cleanup_check = QCheckBox("تنظيف تلقائي للملفات القديمة")
        
        self.keep_days_spin = QSpinBox()
        self.keep_days_spin.setRange(1, 365)
        self.keep_days_spin.setValue(30)
        self.keep_days_spin.setSuffix(" يوم")
        cleanup_layout.addRow("الاحتفاظ بالملفات لمدة:", self.keep_days_spin)
        
        cleanup_group.setLayout(cleanup_layout)
        layout.addWidget(cleanup_group)
        layout.addWidget(self.auto_cleanup_check)
        
        layout.addStretch()
        tab.setLayout(layout)
        return tab
    
    def create_alerts_tab(self):
        """إنشاء تبويب إعدادات التنبيهات"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات التنبيهات الصوتية
        sound_group = QGroupBox("التنبيهات الصوتية")
        sound_layout = QVBoxLayout()
        
        self.sound_enabled_check = QCheckBox("تفعيل التنبيهات الصوتية")
        self.sound_enabled_check.setChecked(True)
        
        volume_layout = QFormLayout()
        self.volume_slider = QSlider(Qt.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(80)
        self.volume_label = QLabel("80%")
        self.volume_slider.valueChanged.connect(
            lambda v: self.volume_label.setText(f"{v}%")
        )
        
        volume_h_layout = QHBoxLayout()
        volume_h_layout.addWidget(self.volume_slider)
        volume_h_layout.addWidget(self.volume_label)
        volume_layout.addRow("مستوى الصوت:", volume_h_layout)
        
        sound_layout.addWidget(self.sound_enabled_check)
        sound_layout.addLayout(volume_layout)
        sound_group.setLayout(sound_layout)
        layout.addWidget(sound_group)
        
        # إعدادات البريد الإلكتروني
        email_group = QGroupBox("تنبيهات البريد الإلكتروني")
        email_layout = QFormLayout()
        
        self.email_enabled_check = QCheckBox("تفعيل تنبيهات البريد الإلكتروني")
        
        self.smtp_server_edit = QLineEdit()
        self.smtp_server_edit.setPlaceholderText("smtp.gmail.com")
        email_layout.addRow("خادم SMTP:", self.smtp_server_edit)
        
        self.smtp_port_spin = QSpinBox()
        self.smtp_port_spin.setRange(1, 65535)
        self.smtp_port_spin.setValue(587)
        email_layout.addRow("منفذ SMTP:", self.smtp_port_spin)
        
        self.email_user_edit = QLineEdit()
        self.email_user_edit.setPlaceholderText("<EMAIL>")
        email_layout.addRow("البريد الإلكتروني:", self.email_user_edit)
        
        self.email_password_edit = QLineEdit()
        self.email_password_edit.setEchoMode(QLineEdit.Password)
        email_layout.addRow("كلمة المرور:", self.email_password_edit)
        
        email_group.setLayout(email_layout)
        layout.addWidget(self.email_enabled_check)
        layout.addWidget(email_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        return tab
    
    def create_system_tab(self):
        """إنشاء تبويب إعدادات النظام"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات الأداء
        performance_group = QGroupBox("مراقبة الأداء")
        performance_layout = QFormLayout()
        
        self.cpu_limit_spin = QSpinBox()
        self.cpu_limit_spin.setRange(10, 100)
        self.cpu_limit_spin.setValue(80)
        self.cpu_limit_spin.setSuffix("%")
        performance_layout.addRow("حد استخدام المعالج:", self.cpu_limit_spin)
        
        self.memory_limit_spin = QSpinBox()
        self.memory_limit_spin.setRange(512, 8192)
        self.memory_limit_spin.setValue(2048)
        self.memory_limit_spin.setSuffix(" MB")
        performance_layout.addRow("حد استخدام الذاكرة:", self.memory_limit_spin)
        
        performance_group.setLayout(performance_layout)
        layout.addWidget(performance_group)
        
        # إعدادات السجلات
        logging_group = QGroupBox("السجلات")
        logging_layout = QFormLayout()
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level_combo.setCurrentText("INFO")
        logging_layout.addRow("مستوى السجل:", self.log_level_combo)
        
        self.max_log_size_spin = QSpinBox()
        self.max_log_size_spin.setRange(1, 1000)
        self.max_log_size_spin.setValue(10)
        self.max_log_size_spin.setSuffix(" MB")
        logging_layout.addRow("حجم السجل الأقصى:", self.max_log_size_spin)
        
        logging_group.setLayout(logging_layout)
        layout.addWidget(logging_group)
        
        # إعدادات النسخ الاحتياطي
        backup_group = QGroupBox("النسخ الاحتياطي")
        backup_layout = QVBoxLayout()
        
        self.auto_backup_check = QCheckBox("نسخ احتياطي تلقائي للإعدادات")
        self.auto_backup_check.setChecked(True)
        
        backup_layout.addWidget(self.auto_backup_check)
        backup_group.setLayout(backup_layout)
        layout.addWidget(backup_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        return tab
    
    def browse_video_path(self):
        """تصفح مجلد الفيديو"""
        try:
            current_path = self.video_path_edit.text() or "videos/"
            path = QFileDialog.getExistingDirectory(
                self,
                "اختر مجلد الفيديو",
                current_path,
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )
            if path:
                self.video_path_edit.setText(path)
                print(f"✅ تم تحديد مجلد الفيديو: {path}")
        except Exception as e:
            print(f"خطأ في تصفح مجلد الفيديو: {e}")
            QMessageBox.warning(self, "خطأ", f"فشل في فتح متصفح المجلدات:\n{str(e)}")

    def browse_snapshot_path(self):
        """تصفح مجلد اللقطات"""
        try:
            current_path = self.snapshot_path_edit.text() or "snapshots/"
            path = QFileDialog.getExistingDirectory(
                self,
                "اختر مجلد اللقطات",
                current_path,
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )
            if path:
                self.snapshot_path_edit.setText(path)
                print(f"✅ تم تحديد مجلد اللقطات: {path}")
        except Exception as e:
            print(f"خطأ في تصفح مجلد اللقطات: {e}")
            QMessageBox.warning(self, "خطأ", f"فشل في فتح متصفح المجلدات:\n{str(e)}")
    
    def load_settings(self):
        """تحميل الإعدادات الحالية"""
        try:
            # تحميل الإعدادات من مدير الإعدادات
            settings = self.settings_manager.get_all_settings()

            # تطبيق الإعدادات على الواجهة
            self.language_combo.setCurrentText(settings.get('language', 'العربية'))
            self.theme_combo.setCurrentText(settings.get('theme', 'مظلم'))
            self.opacity_slider.setValue(int(float(settings.get('opacity', 1.0)) * 100))

            # تحميل مسارات الحفظ
            video_path = settings.get('video_path', 'videos/')
            snapshot_path = settings.get('snapshot_path', 'snapshots/')

            self.video_path_edit.setText(video_path)
            self.snapshot_path_edit.setText(snapshot_path)

            # تحميل إعدادات أخرى
            if hasattr(self, 'auto_start_check'):
                self.auto_start_check.setChecked(settings.get('auto_start', False))
            if hasattr(self, 'minimize_tray_check'):
                self.minimize_tray_check.setChecked(settings.get('minimize_to_tray', True))
            if hasattr(self, 'remember_window_check'):
                self.remember_window_check.setChecked(settings.get('remember_window', True))

            print(f"✅ تم تحميل مسار الفيديو: {video_path}")
            print(f"✅ تم تحميل مسار اللقطات: {snapshot_path}")

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            # تعيين القيم الافتراضية في حالة الخطأ
            self.video_path_edit.setText('videos/')
            self.snapshot_path_edit.setText('snapshots/')
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        try:
            # جمع الإعدادات من الواجهة
            settings = {
                'language': self.language_combo.currentText(),
                'theme': self.theme_combo.currentText(),
                'opacity': self.opacity_slider.value() / 100.0,
                'auto_start': self.auto_start_check.isChecked(),
                'minimize_to_tray': self.minimize_tray_check.isChecked(),
                'remember_window': self.remember_window_check.isChecked(),
                # إعدادات المسارات
                'video_path': self.video_path_edit.text(),
                'snapshot_path': self.snapshot_path_edit.text(),
                # إعدادات أخرى...
            }

            # حفظ الإعدادات
            for key, value in settings.items():
                self.settings_manager.set_setting(key, value)

            # إنشاء المجلدات إذا لم تكن موجودة
            self.create_directories()

            # إرسال إشارة تغيير الإعدادات
            self.settings_changed.emit()

            QMessageBox.information(self, "تم التطبيق", "تم تطبيق الإعدادات بنجاح!")
            print(f"✅ تم حفظ مسار الفيديو: {settings['video_path']}")
            print(f"✅ تم حفظ مسار اللقطات: {settings['snapshot_path']}")

        except Exception as e:
            print(f"خطأ في تطبيق الإعدادات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تطبيق الإعدادات:\n{str(e)}")

    def create_directories(self):
        """إنشاء المجلدات إذا لم تكن موجودة"""
        try:
            import os

            video_path = self.video_path_edit.text()
            snapshot_path = self.snapshot_path_edit.text()

            if video_path and not os.path.exists(video_path):
                os.makedirs(video_path, exist_ok=True)
                print(f"✅ تم إنشاء مجلد الفيديو: {video_path}")

            if snapshot_path and not os.path.exists(snapshot_path):
                os.makedirs(snapshot_path, exist_ok=True)
                print(f"✅ تم إنشاء مجلد اللقطات: {snapshot_path}")

        except Exception as e:
            print(f"خطأ في إنشاء المجلدات: {e}")
    
    def save_settings(self):
        """حفظ الإعدادات والإغلاق"""
        self.apply_settings()
        self.accept()
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        reply = QMessageBox.question(
            self, "إعادة تعيين", 
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.settings_manager.reset_to_defaults()
                self.load_settings()
                QMessageBox.information(self, "تم الإعادة", "تم إعادة تعيين الإعدادات بنجاح!")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في إعادة التعيين:\n{str(e)}")
    
    def apply_style(self):
        """تطبيق النمط"""
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 8px;
                margin: 10px 0;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QComboBox, QSpinBox {
                background-color: #3b3b3b;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 8px;
                color: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus {
                border: 2px solid #2196F3;
            }
            QPushButton {
                background-color: #2196F3;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QCheckBox {
                color: white;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #555;
                background-color: #3b3b3b;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #2196F3;
                background-color: #2196F3;
                border-radius: 3px;
            }
            QSlider::groove:horizontal {
                border: 1px solid #555;
                height: 8px;
                background: #3b3b3b;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #2196F3;
                border: 1px solid #2196F3;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #2b2b2b;
            }
            QTabBar::tab {
                background-color: #3b3b3b;
                color: white;
                padding: 8px 16px;
                margin: 2px;
                border-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
            }
        """)
