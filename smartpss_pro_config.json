{"version": "2.0.0", "created_at": "2025-06-16T14:19:51.391131", "last_updated": "2025-06-16T14:19:51.391154", "application": {"name": "SmartPSS Pro", "language": "ar", "theme": "dark", "auto_start": false, "minimize_to_tray": true, "check_updates": true, "send_analytics": false}, "performance": {"max_concurrent_streams": 16, "video_buffer_size": 10, "memory_limit_mb": 2048, "cpu_usage_limit": 80, "gpu_acceleration": true, "multi_threading": true, "frame_skip_threshold": 5, "optimization_level": "balanced"}, "video": {"default_resolution": "1920x1080", "supported_codecs": ["h264", "h265", "mjpeg"], "hardware_decoding": true, "deinterlacing": true, "noise_reduction": false, "color_correction": false, "hdr_support": false}, "ai_detection": {"enabled": false, "model_path": "models/", "confidence_threshold": 0.7, "nms_threshold": 0.4, "object_classes": ["person", "car", "truck", "bicycle"], "face_recognition": {"enabled": false, "database_path": "faces/", "recognition_threshold": 0.8}, "license_plate": {"enabled": false, "regions": ["saudi", "uae", "egypt"], "confidence_threshold": 0.9}}, "cloud_storage": {"enabled": false, "provider": "none", "auto_upload": false, "upload_schedule": "daily", "encryption": true, "compression": true, "retention_days": 30, "providers": {"google_drive": {"client_id": "", "client_secret": "", "folder_id": ""}, "dropbox": {"app_key": "", "app_secret": "", "access_token": ""}, "aws_s3": {"access_key": "", "secret_key": "", "bucket_name": "", "region": "us-east-1"}}}, "security": {"encryption_enabled": true, "encryption_algorithm": "AES-256", "two_factor_auth": false, "session_timeout": 480, "max_login_attempts": 5, "lockout_duration": 30, "password_policy": {"min_length": 8, "require_uppercase": true, "require_lowercase": true, "require_numbers": true, "require_symbols": false, "expiry_days": 90}, "audit_logging": true, "data_anonymization": false}, "network": {"connection_timeout": 10, "read_timeout": 30, "max_retries": 3, "retry_delay": 2, "bandwidth_limit": 0, "proxy": {"enabled": false, "type": "http", "host": "", "port": 8080, "username": "", "password": ""}, "ssl_verification": true, "custom_certificates": []}, "alerts": {"sound_enabled": true, "sound_volume": 80, "email_enabled": false, "sms_enabled": false, "push_notifications": false, "webhook_enabled": false, "alert_cooldown": 60, "severity_levels": {"low": {"color": "#FFC107", "sound": "beep.wav"}, "medium": {"color": "#FF9800", "sound": "alert.wav"}, "high": {"color": "#F44336", "sound": "alarm.wav"}, "critical": {"color": "#D32F2F", "sound": "emergency.wav"}}}, "backup": {"enabled": true, "schedule": "daily", "time": "02:00", "location": "backups/", "retention_days": 7, "compression": true, "encryption": true, "include_videos": false, "include_logs": true, "include_settings": true, "cloud_backup": false}, "development": {"debug_mode": false, "verbose_logging": false, "performance_profiling": false, "memory_profiling": false, "crash_dumps": true, "telemetry": false, "beta_features": false}, "integrations": {"home_assistant": {"enabled": false, "url": "", "token": ""}, "mqtt": {"enabled": false, "broker": "localhost", "port": 1883, "username": "", "password": "", "topic_prefix": "smartpss"}, "webhook": {"enabled": false, "url": "", "secret": "", "events": ["motion", "recording", "alert"]}}}