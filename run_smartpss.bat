@echo off
title SmartPSS Pro - Smart Monitoring System
color 0A

echo.
echo ========================================
echo    SmartPSS Pro - Smart Monitoring System
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    echo.
    pause
    exit /b 1
)

echo [INFO] Python found
python --version

REM Check if virtual environment exists
if exist "venv\Scripts\activate.bat" (
    echo [INFO] Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo [INFO] No virtual environment found, using system Python
)

REM Check if basic requirements are installed
echo [INFO] Checking basic dependencies...
python -c "import PyQt5" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] PyQt5 is missing
    echo [INFO] Installing basic requirements...
    pip install PyQt5 numpy psutil
    if errorlevel 1 (
        echo [ERROR] Failed to install basic requirements
        echo Please run: pip install PyQt5 numpy psutil
        echo.
        pause
        exit /b 1
    )
)

echo [INFO] Basic dependencies are available
echo.

REM Create necessary directories
if not exist "videos" mkdir videos
if not exist "logs" mkdir logs
if not exist "snapshots" mkdir snapshots
if not exist "sounds" mkdir sounds

echo [INFO] Choose startup option:
echo [1] Simple Test Mode (Recommended for first run)
echo [2] Full System Mode
echo.
set /p choice="Enter your choice (1 or 2): "

if "%choice%"=="1" (
    echo [INFO] Starting SmartPSS Pro in Test Mode...
    python simple_main.py
) else if "%choice%"=="2" (
    echo [INFO] Starting SmartPSS Pro in Full Mode...
    python main.py
) else (
    echo [INFO] Invalid choice, starting in Test Mode...
    python simple_main.py
)

REM Check exit code
if errorlevel 1 (
    echo.
    echo [ERROR] SmartPSS Pro exited with an error
    echo Check the logs folder for more information
    echo.
    pause
) else (
    echo.
    echo [INFO] SmartPSS Pro closed normally
)

REM Deactivate virtual environment if it was activated
if exist "venv\Scripts\activate.bat" (
    deactivate
)

echo.
echo Press any key to exit...
pause >nul
