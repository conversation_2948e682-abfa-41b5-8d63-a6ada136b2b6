@echo off
title SmartPSS - Smart Monitoring System
color 0A

echo.
echo ========================================
echo    SmartPSS - Smart Monitoring System
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    echo.
    pause
    exit /b 1
)

echo [INFO] Python found
python --version

REM Check if virtual environment exists
if exist "venv\Scripts\activate.bat" (
    echo [INFO] Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo [INFO] No virtual environment found, using system Python
)

REM Check if requirements are installed
echo [INFO] Checking dependencies...
python -c "import cv2, PyQt5, numpy" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Some dependencies are missing
    echo [INFO] Installing requirements...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] Failed to install requirements
        echo Please run: pip install -r requirements.txt
        echo.
        pause
        exit /b 1
    )
)

echo [INFO] All dependencies are available
echo.

REM Create necessary directories
if not exist "videos" mkdir videos
if not exist "logs" mkdir logs
if not exist "snapshots" mkdir snapshots
if not exist "sounds" mkdir sounds

echo [INFO] Starting SmartPSS...
echo.

REM Run the application
python main.py

REM Check exit code
if errorlevel 1 (
    echo.
    echo [ERROR] SmartPSS exited with an error
    echo Check the logs folder for more information
    echo.
    pause
) else (
    echo.
    echo [INFO] SmartPSS closed normally
)

REM Deactivate virtual environment if it was activated
if exist "venv\Scripts\activate.bat" (
    deactivate
)

echo.
echo Press any key to exit...
pause >nul
