@echo off
title SmartPSS Pro - Fix and Run
color 0E

echo.
echo ========================================
echo    SmartPSS Pro - Fix and Run
echo ========================================
echo.

echo [INFO] This script will diagnose and fix common issues
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed!
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo [INFO] Python is available
python --version

echo.
echo [INFO] What would you like to do?
echo.
echo [1] Run System Diagnosis (Recommended)
echo [2] Run Minimal Test
echo [3] Fix PyQt5 Installation
echo [4] Install All Requirements
echo [5] Run Safe Mode
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" (
    echo.
    echo [INFO] Running system diagnosis...
    python diagnose.py
    goto :end
)

if "%choice%"=="2" (
    echo.
    echo [INFO] Running minimal test...
    python minimal_test.py
    goto :end
)

if "%choice%"=="3" (
    echo.
    echo [INFO] Fixing PyQt5 installation...
    echo [INFO] Uninstalling PyQt5...
    pip uninstall PyQt5 -y
    echo [INFO] Installing PyQt5...
    pip install PyQt5
    echo [INFO] Testing PyQt5...
    python -c "import PyQt5; print('PyQt5 is working!')"
    if errorlevel 1 (
        echo [ERROR] PyQt5 installation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] PyQt5 is now working!
    echo [INFO] Running test...
    python minimal_test.py
    goto :end
)

if "%choice%"=="4" (
    echo.
    echo [INFO] Installing all requirements...
    pip install PyQt5 numpy psutil opencv-python
    echo [INFO] Testing installation...
    python diagnose.py
    goto :end
)

if "%choice%"=="5" (
    echo.
    echo [INFO] Running safe mode...
    python safe_main.py
    goto :end
)

echo [ERROR] Invalid choice
pause
exit /b 1

:end
echo.
echo [INFO] Operation completed
pause
