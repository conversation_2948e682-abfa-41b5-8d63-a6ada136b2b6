#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for adding camera
اختبار إضافة كاميرا
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager
from camera_manager import CameraManager

def test_add_camera():
    """اختبار إضافة كاميرا"""
    
    print("🧪 اختبار إضافة كاميرا...")
    
    try:
        # إنشاء مدير الكاميرات
        camera_manager = CameraManager()
        
        # بيانات كاميرا تجريبية (USB Camera)
        camera_data = {
            'name': 'كاميرا تجريبية',
            'ip_address': 'localhost',
            'port': 0,
            'username': '',
            'password': '',
            'rtsp_url': '0',  # USB Camera
            'location': 'مكتب الاختبار',
            'motion_detection': True,
            'recording_enabled': True
        }
        
        print("📝 بيانات الكاميرا:")
        for key, value in camera_data.items():
            print(f"   {key}: {value}")
        
        # إضافة الكاميرا
        print("\n➕ إضافة الكاميرا...")
        camera_id = camera_manager.add_camera(camera_data)
        
        if camera_id:
            print(f"✅ تم إضافة الكاميرا بنجاح! ID: {camera_id}")
            
            # الحصول على قائمة الكاميرات
            cameras = camera_manager.get_cameras()
            print(f"\n📹 عدد الكاميرات: {len(cameras)}")
            
            for camera in cameras:
                print(f"   - {camera}")
                
        else:
            print("❌ فشل في إضافة الكاميرا")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def test_database_direct():
    """اختبار قاعدة البيانات مباشرة"""
    
    print("\n🗄️ اختبار قاعدة البيانات مباشرة...")
    
    try:
        db = DatabaseManager()
        
        # بيانات كاميرا بسيطة
        camera_data = {
            'name': 'كاميرا مباشرة',
            'ip_address': '*************',
            'port': 554,
            'username': 'admin',
            'password': 'admin123',
            'rtsp_url': 'rtsp://*************:554/stream',
            'location': 'اختبار مباشر',
            'motion_detection': True,
            'recording_enabled': True
        }
        
        print("📝 بيانات الكاميرا:")
        for key, value in camera_data.items():
            print(f"   {key}: {value}")
        
        # إضافة للقاعدة مباشرة
        print("\n➕ إضافة للقاعدة مباشرة...")
        camera_id = db.add_camera(camera_data)
        
        if camera_id:
            print(f"✅ تم إضافة الكاميرا للقاعدة! ID: {camera_id}")
            
            # الحصول على الكاميرات
            cameras = db.get_cameras()
            print(f"\n📹 عدد الكاميرات في القاعدة: {len(cameras)}")
            
            for camera in cameras:
                print(f"   - {camera}")
                
        else:
            print("❌ فشل في إضافة الكاميرا للقاعدة")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار القاعدة: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 بدء اختبارات إضافة الكاميرا")
    print("=" * 50)
    
    # اختبار قاعدة البيانات مباشرة
    test_database_direct()
    
    # اختبار مدير الكاميرات
    test_add_camera()
    
    print("\n" + "=" * 50)
    print("✅ انتهاء الاختبارات")
