#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SmartPSS Pro - Simple Main Entry Point
نقطة دخول مبسطة لاختبار النظام
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def check_basic_requirements():
    """فحص المتطلبات الأساسية"""
    print("🔍 فحص المتطلبات الأساسية...")
    
    missing = []
    
    # فحص PyQt5
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 متوفر")
    except ImportError:
        missing.append("PyQt5")
        print("❌ PyQt5 غير متوفر")
    
    # فحص OpenCV
    try:
        import cv2
        print("✅ OpenCV متوفر")
    except ImportError:
        missing.append("opencv-python")
        print("❌ OpenCV غير متوفر")
    
    # فحص NumPy
    try:
        import numpy
        print("✅ NumPy متوفر")
    except ImportError:
        missing.append("numpy")
        print("❌ NumPy غير متوفر")
    
    # فحص psutil
    try:
        import psutil
        print("✅ psutil متوفر")
    except ImportError:
        missing.append("psutil")
        print("❌ psutil غير متوفر")
    
    if missing:
        print(f"\n❌ مكتبات مفقودة: {', '.join(missing)}")
        print("لتثبيت المكتبات المفقودة:")
        print(f"pip install {' '.join(missing)}")
        return False
    
    print("\n✅ جميع المتطلبات الأساسية متوفرة!")
    return True

def create_simple_app():
    """إنشاء تطبيق بسيط للاختبار"""
    try:
        from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, 
                                   QVBoxLayout, QLabel, QPushButton, QMessageBox)
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        class SimpleMainWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("SmartPSS Pro - اختبار النظام")
                self.setGeometry(100, 100, 600, 400)
                
                # الودجة المركزية
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                # التخطيط
                layout = QVBoxLayout()
                
                # العنوان
                title = QLabel("SmartPSS Pro")
                title.setFont(QFont("Arial", 24, QFont.Bold))
                title.setAlignment(Qt.AlignCenter)
                title.setStyleSheet("color: #2196F3; margin: 20px;")
                
                # الوصف
                description = QLabel("نظام المراقبة الذكي المتطور")
                description.setFont(QFont("Arial", 16))
                description.setAlignment(Qt.AlignCenter)
                description.setStyleSheet("color: #666; margin: 10px;")
                
                # معلومات النظام
                info_label = QLabel("تم تحميل النظام بنجاح!")
                info_label.setAlignment(Qt.AlignCenter)
                info_label.setStyleSheet("color: #4CAF50; font-size: 14px; margin: 20px;")
                
                # أزرار الاختبار
                test_db_btn = QPushButton("اختبار قاعدة البيانات")
                test_db_btn.clicked.connect(self.test_database)
                
                test_settings_btn = QPushButton("اختبار الإعدادات")
                test_settings_btn.clicked.connect(self.test_settings)
                
                test_ui_btn = QPushButton("اختبار الواجهة الكاملة")
                test_ui_btn.clicked.connect(self.test_full_ui)
                
                # إضافة العناصر للتخطيط
                layout.addWidget(title)
                layout.addWidget(description)
                layout.addWidget(info_label)
                layout.addWidget(test_db_btn)
                layout.addWidget(test_settings_btn)
                layout.addWidget(test_ui_btn)
                layout.addStretch()
                
                central_widget.setLayout(layout)
                
                # تطبيق نمط مظلم
                self.setStyleSheet("""
                    QMainWindow {
                        background-color: #2b2b2b;
                        color: white;
                    }
                    QWidget {
                        background-color: #2b2b2b;
                        color: white;
                    }
                    QPushButton {
                        background-color: #2196F3;
                        border: none;
                        border-radius: 8px;
                        padding: 12px;
                        font-size: 14px;
                        font-weight: bold;
                        color: white;
                        margin: 5px;
                    }
                    QPushButton:hover {
                        background-color: #1976D2;
                    }
                    QPushButton:pressed {
                        background-color: #0D47A1;
                    }
                """)
            
            def test_database(self):
                """اختبار قاعدة البيانات"""
                try:
                    from database import DatabaseManager
                    db = DatabaseManager()
                    QMessageBox.information(self, "نجح الاختبار", "قاعدة البيانات تعمل بشكل صحيح!")
                except Exception as e:
                    QMessageBox.critical(self, "فشل الاختبار", f"خطأ في قاعدة البيانات:\n{str(e)}")
            
            def test_settings(self):
                """اختبار الإعدادات"""
                try:
                    from settings import SettingsManager
                    settings = SettingsManager()
                    theme = settings.get_setting('theme', 'dark')
                    QMessageBox.information(self, "نجح الاختبار", f"الإعدادات تعمل بشكل صحيح!\nالمظهر الحالي: {theme}")
                except Exception as e:
                    QMessageBox.critical(self, "فشل الاختبار", f"خطأ في الإعدادات:\n{str(e)}")
            
            def test_full_ui(self):
                """اختبار الواجهة الكاملة"""
                try:
                    from ui.main_window import MainWindow
                    self.full_window = MainWindow()
                    self.full_window.show()
                    QMessageBox.information(self, "نجح الاختبار", "تم فتح الواجهة الكاملة!")
                except Exception as e:
                    QMessageBox.critical(self, "فشل الاختبار", f"خطأ في الواجهة الكاملة:\n{str(e)}")
        
        return SimpleMainWindow
        
    except Exception as e:
        print(f"خطأ في إنشاء التطبيق البسيط: {e}")
        return None

def main():
    """الدالة الرئيسية المبسطة"""
    print("=" * 50)
    print("🚀 SmartPSS Pro - اختبار النظام")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_basic_requirements():
        input("\nاضغط Enter للخروج...")
        return 1
    
    try:
        # إنشاء التطبيق
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication(sys.argv)
        app.setApplicationName("SmartPSS Pro Test")
        
        # إنشاء النافذة البسيطة
        SimpleMainWindow = create_simple_app()
        if SimpleMainWindow is None:
            print("❌ فشل في إنشاء التطبيق")
            return 1
        
        window = SimpleMainWindow()
        window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("🎯 يمكنك الآن اختبار المكونات المختلفة")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("\nاضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
