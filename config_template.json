{"application": {"name": "SmartPSS", "version": "1.0.0", "language": "ar", "theme": "dark"}, "database": {"path": "database.db", "backup_enabled": true, "backup_interval_hours": 24}, "video": {"default_quality": "high", "fps_limit": 30, "grid_layout": "2x2", "buffer_size": 1}, "recording": {"storage_path": "./videos/", "default_quality": "high", "format": "mp4", "auto_cleanup_enabled": true, "auto_cleanup_days": 30, "max_storage_gb": 100}, "motion_detection": {"enabled": true, "default_sensitivity": "medium", "auto_recording": true, "auto_recording_duration": 300, "snapshot_enabled": true, "snapshot_path": "./snapshots/"}, "alerts": {"sound_enabled": true, "sound_file": "default", "email_enabled": false, "email_settings": {"smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "", "password": "", "recipient": ""}, "system_tray": true}, "network": {"rtsp_timeout": 10, "reconnect_attempts": 5, "discovery_enabled": true, "discovery_range": "***********/24"}, "ui": {"window_opacity": 1.0, "minimize_to_tray": true, "startup_with_system": false, "remember_window_size": true, "show_tooltips": true}, "logging": {"level": "INFO", "file_enabled": true, "console_enabled": true, "max_file_size_mb": 10, "backup_count": 5}, "security": {"session_timeout_hours": 8, "max_failed_attempts": 5, "lockout_duration_minutes": 30, "password_min_length": 8}, "performance": {"max_concurrent_streams": 16, "frame_skip_threshold": 5, "memory_limit_mb": 1024, "cpu_limit_percent": 80}, "backup": {"enabled": true, "path": "./backups/", "schedule": "daily", "keep_days": 7, "include_videos": false}}