# SmartPSS - نظام المراقبة الذكي

نظام مراقبة متقدم مشابه لبرنامج SmartPSS مطور بلغة Python مع واجهة مستخدم باللغة العربية ومظهر أسود أنيق.

## الميزات الرئيسية

### 🎥 إدارة الكاميرات
- إضافة وتعديل وحذف الكاميرات
- دعم بروتوكول RTSP للبث المباشر
- دعم ONVIF لإدارة الكاميرات
- اكتشاف تلقائي للكاميرات في الشبكة
- اختبار الاتصال قبل الحفظ

### 📹 البث المباشر
- عرض متعدد الكاميرات في شبكة قابلة للتخصيص
- جودة فيديو قابلة للتعديل (منخفضة، متوسطة، عالية)
- مؤشرات حالة الاتصال والتسجيل والحركة
- تحكم في معدل الإطارات (FPS)

### 🔍 كشف الحركة
- كشف الحركة باستخدام OpenCV
- مستويات حساسية قابلة للتعديل
- حفظ لقطات عند اكتشاف الحركة
- تسجيل تلقائي عند الحركة

### 📼 التسجيل
- تسجيل يدوي وتلقائي
- جدولة التسجيل حسب الوقت
- تسجيل عند اكتشاف الحركة
- تنسيقات متعددة (MP4, AVI, MKV)
- جودة تسجيل قابلة للتعديل

### 🎬 إعادة التشغيل
- تشغيل التسجيلات مع تحكم كامل
- شريط زمني للتنقل
- تصدير مقاطع محددة
- بحث في التسجيلات حسب التاريخ والوقت

### 🔔 نظام التنبيهات
- تنبيهات صوتية عند اكتشاف الحركة
- إشعارات في شريط النظام
- تنبيهات عبر البريد الإلكتروني
- سجل شامل للأحداث

### 👥 إدارة المستخدمين
- نظام مستخدمين متعدد المستويات
- أدوار مختلفة (مدير، مشغل، مشاهد)
- تسجيل دخول آمن مع تشفير كلمات المرور
- جلسات محدودة الوقت

### 🎨 الواجهة والمظهر
- واجهة باللغة العربية مع دعم RTL
- مظهر أسود أنيق ومتطور
- قابلية تخصيص الألوان والشفافية
- تصغير إلى شريط النظام

## متطلبات النظام

### البرمجيات المطلوبة
- Python 3.8 أو أحدث
- نظام التشغيل: Windows 10/11, Linux, macOS

### المكتبات المطلوبة
```
opencv-python==********
PyQt5==5.15.10
onvif-zeep==0.2.12
ffmpeg-python==0.2.0
numpy==1.24.3
Pillow==10.0.1
requests==2.31.0
python-vlc==3.0.18121
schedule==1.2.0
playsound==1.3.0
pyinstaller==6.1.0
```

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/smartpss.git
cd smartpss
```

### 2. إنشاء بيئة افتراضية (اختياري)
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. تشغيل البرنامج
```bash
python main.py
```

## الاستخدام

### تسجيل الدخول الأولي
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### إضافة كاميرا جديدة
1. اضغط على "إضافة كاميرا" في شريط الأدوات
2. أدخل معلومات الكاميرا:
   - الاسم والموقع
   - عنوان IP والمنفذ
   - اسم المستخدم وكلمة المرور
   - رابط RTSP
3. اختبر الاتصال قبل الحفظ
4. احفظ الإعدادات

### بدء التسجيل
- **تسجيل يدوي**: اضغط على زر "تسجيل" في نافذة الكاميرا
- **تسجيل تلقائي**: فعّل كشف الحركة في إعدادات الكاميرا
- **تسجيل مجدول**: استخدم قائمة الأدوات لجدولة التسجيل

### عرض التسجيلات
1. انتقل إلى تبويب "التسجيل"
2. اختر التسجيل المطلوب من القائمة
3. اضغط على "تشغيل" لبدء العرض

## هيكل المشروع

```
smartpss/
├── main.py                 # نقطة دخول التطبيق
├── database.py            # إدارة قاعدة البيانات
├── camera_manager.py      # إدارة الكاميرات
├── motion_detector.py     # كشف الحركة
├── recorder.py            # التسجيل
├── playback.py           # إعادة التشغيل
├── user_manager.py       # إدارة المستخدمين
├── settings.py           # إدارة الإعدادات
├── alerts.py             # نظام التنبيهات
├── ui/                   # واجهة المستخدم
│   ├── main_window.py    # النافذة الرئيسية
│   ├── login_dialog.py   # نافذة تسجيل الدخول
│   └── camera_dialog.py  # نافذة إعدادات الكاميرا
├── videos/               # مجلد التسجيلات
├── logs/                 # ملفات السجل
├── snapshots/           # لقطات الحركة
├── database.db          # قاعدة البيانات
├── settings.json        # ملف الإعدادات
└── requirements.txt     # متطلبات Python
```

## إنشاء ملف تنفيذي (EXE)

لإنشاء ملف تنفيذي مستقل:

```bash
pyinstaller --noconsole --onefile --name SmartPSS main.py
```

سيتم إنشاء الملف التنفيذي في مجلد `dist/`

## الإعدادات المتقدمة

### إعدادات قاعدة البيانات
- قاعدة البيانات: SQLite
- الملف: `database.db`
- النسخ الاحتياطي: تلقائي يومياً

### إعدادات الشبكة
- مهلة الاتصال: 10 ثوانٍ
- محاولات إعادة الاتصال: 5 مرات
- حجم المخزن المؤقت: قابل للتعديل

### إعدادات التسجيل
- المسار الافتراضي: `./videos/`
- التنظيف التلقائي: 30 يوم
- الجودة: منخفضة/متوسطة/عالية

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. فشل الاتصال بالكاميرا**
- تأكد من صحة عنوان IP
- تحقق من اسم المستخدم وكلمة المرور
- تأكد من تشغيل الكاميرا ووصولها للشبكة

**2. عدم عمل كشف الحركة**
- تحقق من إعدادات الحساسية
- تأكد من تفعيل كشف الحركة للكاميرا
- راجع سجل الأخطاء في مجلد `logs/`

**3. مشاكل في التسجيل**
- تحقق من مساحة القرص الصلب
- تأكد من صلاحيات الكتابة في مجلد التسجيل
- راجع إعدادات جودة التسجيل

## المساهمة في المشروع

نرحب بمساهماتكم في تطوير المشروع:

1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- مراسلة المطورين
- مراجعة الوثائق

## الإصدارات القادمة

### الميزات المخططة
- [ ] دعم كاميرات IP إضافية
- [ ] تحليل ذكي للفيديو
- [ ] تطبيق جوال مصاحب
- [ ] تكامل مع أنظمة الأمان الأخرى
- [ ] تقارير مفصلة ورسوم بيانية
- [ ] دعم التخزين السحابي

---

**SmartPSS** - نظام مراقبة ذكي ومتطور لجميع احتياجاتك الأمنية
