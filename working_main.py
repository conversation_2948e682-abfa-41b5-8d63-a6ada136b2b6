#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SmartPSS Pro - Working Main Entry Point
نقطة دخول تعمل بشكل صحيح
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def main():
    """الدالة الرئيسية التي تعمل"""
    print("🚀 SmartPSS Pro - Working Version")
    print("=" * 50)
    
    try:
        # استيراد PyQt5
        from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, 
                                   QVBoxLayout, QHBoxLayout, QLabel, 
                                   QPushButton, QTabWidget, QMessageBox,
                                   QSplitter, QScrollArea, QGridLayout)
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QFont
        
        print("✅ تم استيراد PyQt5 بنجاح")
        
        # استيراد الوحدات الأساسية
        try:
            from database import DatabaseManager
            from settings import SettingsManager
            from user_manager import UserManager
            print("✅ تم استيراد الوحدات الأساسية")
        except ImportError as e:
            print(f"⚠️ بعض الوحدات غير متوفرة: {e}")
        
        class WorkingMainWindow(QMainWindow):
            """النافذة الرئيسية التي تعمل"""
            
            def __init__(self):
                super().__init__()
                self.init_ui()
                
            def init_ui(self):
                """تهيئة الواجهة"""
                self.setWindowTitle("SmartPSS Pro - Working Version")
                self.setGeometry(200, 200, 1000, 700)
                
                # الودجة المركزية
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                # التخطيط الرئيسي
                main_layout = QVBoxLayout()
                
                # العنوان
                title = QLabel("SmartPSS Pro")
                title.setFont(QFont("Arial", 24, QFont.Bold))
                title.setAlignment(Qt.AlignCenter)
                title.setStyleSheet("color: #2196F3; margin: 20px;")
                
                # إنشاء التبويبات
                self.tabs = QTabWidget()
                
                # تبويب المراقبة
                monitoring_tab = self.create_monitoring_tab()
                self.tabs.addTab(monitoring_tab, "🎥 المراقبة")
                
                # تبويب الإعدادات
                settings_tab = self.create_settings_tab()
                self.tabs.addTab(settings_tab, "⚙️ الإعدادات")
                
                # تبويب معلومات النظام
                info_tab = self.create_info_tab()
                self.tabs.addTab(info_tab, "ℹ️ معلومات النظام")
                
                # إضافة العناصر
                main_layout.addWidget(title)
                main_layout.addWidget(self.tabs)
                
                central_widget.setLayout(main_layout)
                
                # تطبيق نمط مظلم
                self.apply_dark_theme()
                
                # إعداد شريط الحالة
                self.statusBar().showMessage("SmartPSS Pro جاهز للعمل")
                
            def create_monitoring_tab(self):
                """إنشاء تبويب المراقبة"""
                tab = QWidget()
                layout = QHBoxLayout()
                
                # مقسم للكاميرات والتحكم
                splitter = QSplitter(Qt.Horizontal)
                
                # منطقة الكاميرات
                camera_area = QWidget()
                camera_layout = QVBoxLayout()
                
                camera_label = QLabel("منطقة عرض الكاميرات")
                camera_label.setAlignment(Qt.AlignCenter)
                camera_label.setStyleSheet("border: 2px dashed #666; padding: 50px; color: #666;")
                
                camera_layout.addWidget(camera_label)
                camera_area.setLayout(camera_layout)
                
                # منطقة التحكم
                control_area = QWidget()
                control_layout = QVBoxLayout()
                
                control_label = QLabel("لوحة التحكم")
                control_label.setFont(QFont("Arial", 16, QFont.Bold))
                control_label.setAlignment(Qt.AlignCenter)
                
                # أزرار التحكم
                add_camera_btn = QPushButton("➕ إضافة كاميرا")
                add_camera_btn.clicked.connect(self.add_camera)
                
                start_recording_btn = QPushButton("🔴 بدء التسجيل")
                start_recording_btn.clicked.connect(self.start_recording)
                
                stop_recording_btn = QPushButton("⏹️ إيقاف التسجيل")
                stop_recording_btn.clicked.connect(self.stop_recording)
                
                settings_btn = QPushButton("⚙️ الإعدادات")
                settings_btn.clicked.connect(self.show_settings)
                
                control_layout.addWidget(control_label)
                control_layout.addWidget(add_camera_btn)
                control_layout.addWidget(start_recording_btn)
                control_layout.addWidget(stop_recording_btn)
                control_layout.addWidget(settings_btn)
                control_layout.addStretch()
                
                control_area.setLayout(control_layout)
                
                # إضافة للمقسم
                splitter.addWidget(camera_area)
                splitter.addWidget(control_area)
                splitter.setSizes([700, 300])
                
                layout.addWidget(splitter)
                tab.setLayout(layout)
                
                return tab
            
            def create_settings_tab(self):
                """إنشاء تبويب الإعدادات"""
                tab = QWidget()
                layout = QVBoxLayout()
                
                settings_label = QLabel("إعدادات النظام")
                settings_label.setFont(QFont("Arial", 18, QFont.Bold))
                settings_label.setAlignment(Qt.AlignCenter)
                
                # أزرار الإعدادات
                camera_settings_btn = QPushButton("🎥 إعدادات الكاميرات")
                recording_settings_btn = QPushButton("📹 إعدادات التسجيل")
                alert_settings_btn = QPushButton("🔔 إعدادات التنبيهات")
                system_settings_btn = QPushButton("🖥️ إعدادات النظام")
                
                layout.addWidget(settings_label)
                layout.addWidget(camera_settings_btn)
                layout.addWidget(recording_settings_btn)
                layout.addWidget(alert_settings_btn)
                layout.addWidget(system_settings_btn)
                layout.addStretch()
                
                tab.setLayout(layout)
                return tab
            
            def create_info_tab(self):
                """إنشاء تبويب معلومات النظام"""
                tab = QWidget()
                layout = QVBoxLayout()
                
                info_label = QLabel("معلومات النظام")
                info_label.setFont(QFont("Arial", 18, QFont.Bold))
                info_label.setAlignment(Qt.AlignCenter)
                
                # معلومات النظام
                system_info = f"""
                🖥️ نظام التشغيل: {os.name}
                🐍 إصدار Python: {sys.version.split()[0]}
                📁 مجلد العمل: {current_dir}
                
                📊 حالة النظام: يعمل بشكل طبيعي
                🎥 الكاميرات المتصلة: 0
                📹 التسجيلات النشطة: 0
                🔔 التنبيهات: 0
                """
                
                info_text = QLabel(system_info)
                info_text.setAlignment(Qt.AlignLeft)
                info_text.setStyleSheet("background-color: #2D2D2D; padding: 20px; border-radius: 8px;")
                
                # أزرار الاختبار
                test_db_btn = QPushButton("🗄️ اختبار قاعدة البيانات")
                test_db_btn.clicked.connect(self.test_database)
                
                test_settings_btn = QPushButton("⚙️ اختبار الإعدادات")
                test_settings_btn.clicked.connect(self.test_settings)
                
                layout.addWidget(info_label)
                layout.addWidget(info_text)
                layout.addWidget(test_db_btn)
                layout.addWidget(test_settings_btn)
                layout.addStretch()
                
                tab.setLayout(layout)
                return tab
            
            def apply_dark_theme(self):
                """تطبيق المظهر المظلم"""
                self.setStyleSheet("""
                    QMainWindow {
                        background-color: #2b2b2b;
                        color: white;
                    }
                    QWidget {
                        background-color: #2b2b2b;
                        color: white;
                    }
                    QTabWidget::pane {
                        border: 1px solid #555;
                        background-color: #2b2b2b;
                    }
                    QTabBar::tab {
                        background-color: #3b3b3b;
                        color: white;
                        padding: 8px 16px;
                        margin: 2px;
                        border-radius: 4px;
                    }
                    QTabBar::tab:selected {
                        background-color: #2196F3;
                    }
                    QPushButton {
                        background-color: #2196F3;
                        border: none;
                        border-radius: 6px;
                        padding: 10px;
                        font-size: 14px;
                        color: white;
                        margin: 5px;
                    }
                    QPushButton:hover {
                        background-color: #1976D2;
                    }
                    QPushButton:pressed {
                        background-color: #0D47A1;
                    }
                    QLabel {
                        color: white;
                    }
                """)
            
            # وظائف الأزرار
            def add_camera(self):
                QMessageBox.information(self, "إضافة كاميرا", "سيتم إضافة ميزة إضافة الكاميرات قريباً!")
            
            def start_recording(self):
                QMessageBox.information(self, "بدء التسجيل", "تم بدء التسجيل!")
                self.statusBar().showMessage("التسجيل نشط...")
            
            def stop_recording(self):
                QMessageBox.information(self, "إيقاف التسجيل", "تم إيقاف التسجيل!")
                self.statusBar().showMessage("SmartPSS Pro جاهز للعمل")
            
            def show_settings(self):
                QMessageBox.information(self, "الإعدادات", "سيتم فتح نافذة الإعدادات قريباً!")
            
            def test_database(self):
                try:
                    from database import DatabaseManager
                    db = DatabaseManager()
                    QMessageBox.information(self, "اختبار قاعدة البيانات", "✅ قاعدة البيانات تعمل بشكل صحيح!")
                except Exception as e:
                    QMessageBox.warning(self, "اختبار قاعدة البيانات", f"⚠️ خطأ في قاعدة البيانات:\n{str(e)}")
            
            def test_settings(self):
                try:
                    from settings import SettingsManager
                    settings = SettingsManager()
                    QMessageBox.information(self, "اختبار الإعدادات", "✅ الإعدادات تعمل بشكل صحيح!")
                except Exception as e:
                    QMessageBox.warning(self, "اختبار الإعدادات", f"⚠️ خطأ في الإعدادات:\n{str(e)}")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("SmartPSS Pro Working")
        
        # إنشاء النافذة
        window = WorkingMainWindow()
        window.show()
        
        print("✅ تم تشغيل SmartPSS Pro بنجاح!")
        print("🎯 النظام جاهز للاستخدام")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        print(f"التفاصيل: {traceback.format_exc()}")
        input("اضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
