#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database management module for SmartPSS-like monitoring system
Handles SQLite database operations for cameras, users, recordings, and logs
"""

import sqlite3
import hashlib
import json
from datetime import datetime
import os

class DatabaseManager:
    def __init__(self, db_path="database.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize database with required tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'viewer',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Cameras table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cameras (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                ip_address TEXT NOT NULL,
                port INTEGER DEFAULT 554,
                username TEXT,
                password TEXT,
                rtsp_url TEXT NOT NULL,
                onvif_url TEXT,
                location TEXT,
                is_active BOOLEAN DEFAULT 1,
                motion_detection BOOLEAN DEFAULT 1,
                recording_enabled BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Recordings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recordings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER NOT NULL,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                start_time TIMESTAMP NOT NULL,
                end_time TIMESTAMP,
                duration INTEGER,
                file_size INTEGER,
                recording_type TEXT DEFAULT 'manual',
                motion_triggered BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (camera_id) REFERENCES cameras (id)
            )
        ''')
        
        # Motion events table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS motion_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                camera_id INTEGER NOT NULL,
                event_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                confidence REAL,
                recording_id INTEGER,
                snapshot_path TEXT,
                FOREIGN KEY (camera_id) REFERENCES cameras (id),
                FOREIGN KEY (recording_id) REFERENCES recordings (id)
            )
        ''')
        
        # System logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT NOT NULL,
                message TEXT NOT NULL,
                module TEXT,
                user_id INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Settings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create default admin user if no users exist
        cursor.execute("SELECT COUNT(*) FROM users")
        if cursor.fetchone()[0] == 0:
            self.create_user("admin", "admin123", "admin")
        
        # Insert default settings
        default_settings = {
            "language": "ar",
            "theme": "dark",
            "motion_sensitivity": "medium",
            "recording_quality": "high",
            "alert_sound": "true",
            "auto_cleanup_days": "30"
        }
        
        for key, value in default_settings.items():
            cursor.execute(
                "INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)",
                (key, value)
            )
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password):
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def create_user(self, username, password, role="viewer"):
        """Create a new user"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            password_hash = self.hash_password(password)
            cursor.execute(
                "INSERT INTO users (username, password_hash, role) VALUES (?, ?, ?)",
                (username, password_hash, role)
            )
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def authenticate_user(self, username, password):
        """Authenticate user login"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        password_hash = self.hash_password(password)
        cursor.execute(
            "SELECT id, username, role FROM users WHERE username = ? AND password_hash = ? AND is_active = 1",
            (username, password_hash)
        )
        
        user = cursor.fetchone()
        if user:
            # Update last login
            cursor.execute(
                "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
                (user[0],)
            )
            conn.commit()
        
        conn.close()
        return user
    
    def add_camera(self, camera_data):
        """Add a new camera"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO cameras (name, ip_address, port, username, password, rtsp_url, 
                               onvif_url, location, motion_detection, recording_enabled)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            camera_data['name'], camera_data['ip_address'], camera_data['port'],
            camera_data['username'], camera_data['password'], camera_data['rtsp_url'],
            camera_data.get('onvif_url', ''), camera_data.get('location', ''),
            camera_data.get('motion_detection', True), camera_data.get('recording_enabled', True)
        ))
        
        camera_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return camera_id
    
    def get_cameras(self, active_only=True):
        """Get all cameras"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = "SELECT * FROM cameras"
        if active_only:
            query += " WHERE is_active = 1"
        
        cursor.execute(query)
        cameras = cursor.fetchall()
        conn.close()
        return cameras
    
    def update_camera(self, camera_id, camera_data):
        """Update camera information"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE cameras SET name = ?, ip_address = ?, port = ?, username = ?, 
                             password = ?, rtsp_url = ?, onvif_url = ?, location = ?,
                             motion_detection = ?, recording_enabled = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (
            camera_data['name'], camera_data['ip_address'], camera_data['port'],
            camera_data['username'], camera_data['password'], camera_data['rtsp_url'],
            camera_data.get('onvif_url', ''), camera_data.get('location', ''),
            camera_data.get('motion_detection', True), camera_data.get('recording_enabled', True),
            camera_id
        ))
        
        conn.commit()
        conn.close()
    
    def delete_camera(self, camera_id):
        """Soft delete camera"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("UPDATE cameras SET is_active = 0 WHERE id = ?", (camera_id,))
        conn.commit()
        conn.close()

    def remove_camera(self, camera_id):
        """Permanently remove camera and all related data"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حذف التسجيلات المرتبطة بالكاميرا
            cursor.execute("DELETE FROM recordings WHERE camera_id = ?", (camera_id,))

            # حذف أحداث كشف الحركة المرتبطة بالكاميرا
            cursor.execute("DELETE FROM motion_events WHERE camera_id = ?", (camera_id,))

            # حذف الكاميرا نفسها
            cursor.execute("DELETE FROM cameras WHERE id = ?", (camera_id,))

            # التحقق من نجاح الحذف
            rows_affected = cursor.rowcount

            conn.commit()
            conn.close()

            return rows_affected > 0

        except Exception as e:
            print(f"خطأ في حذف الكاميرا من قاعدة البيانات: {e}")
            return False
    
    def add_recording(self, recording_data):
        """Add recording entry"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO recordings (camera_id, filename, file_path, start_time, 
                                  end_time, duration, file_size, recording_type, motion_triggered)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            recording_data['camera_id'], recording_data['filename'], recording_data['file_path'],
            recording_data['start_time'], recording_data.get('end_time'),
            recording_data.get('duration'), recording_data.get('file_size'),
            recording_data.get('recording_type', 'manual'), recording_data.get('motion_triggered', False)
        ))
        
        recording_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return recording_id
    
    def get_recordings(self, camera_id=None, limit=100):
        """Get recordings"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if camera_id:
            cursor.execute('''
                SELECT r.*, c.name as camera_name 
                FROM recordings r 
                JOIN cameras c ON r.camera_id = c.id 
                WHERE r.camera_id = ? 
                ORDER BY r.start_time DESC 
                LIMIT ?
            ''', (camera_id, limit))
        else:
            cursor.execute('''
                SELECT r.*, c.name as camera_name 
                FROM recordings r 
                JOIN cameras c ON r.camera_id = c.id 
                ORDER BY r.start_time DESC 
                LIMIT ?
            ''', (limit,))
        
        recordings = cursor.fetchall()
        conn.close()
        return recordings
    
    def add_motion_event(self, camera_id, confidence, recording_id=None, snapshot_path=None):
        """Add motion detection event"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO motion_events (camera_id, confidence, recording_id, snapshot_path)
            VALUES (?, ?, ?, ?)
        ''', (camera_id, confidence, recording_id, snapshot_path))
        
        conn.commit()
        conn.close()
    
    def log_event(self, level, message, module=None, user_id=None):
        """Log system event"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO system_logs (level, message, module, user_id)
            VALUES (?, ?, ?, ?)
        ''', (level, message, module, user_id))
        
        conn.commit()
        conn.close()
    
    def get_setting(self, key, default=None):
        """Get setting value"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT value FROM settings WHERE key = ?", (key,))
        result = cursor.fetchone()
        conn.close()
        
        return result[0] if result else default
    
    def set_setting(self, key, value):
        """Set setting value"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO settings (key, value, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP)
        ''', (key, value))
        
        conn.commit()
        conn.close()
