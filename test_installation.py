#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Installation test script for SmartPSS
Verifies all dependencies and basic functionality
"""

import sys
import os
import importlib
from datetime import datetime

def test_python_version():
    """Test Python version compatibility"""
    print("Testing Python version...")
    
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def test_dependencies():
    """Test required dependencies"""
    print("\nTesting dependencies...")
    
    dependencies = [
        ('cv2', 'opencv-python'),
        ('PyQt5', 'PyQt5'),
        ('numpy', 'numpy'),
        ('sqlite3', 'sqlite3 (built-in)'),
        ('requests', 'requests'),
        ('schedule', 'schedule'),
    ]
    
    optional_dependencies = [
        ('playsound', 'playsound'),
        ('PIL', 'Pillow'),
    ]
    
    all_good = True
    
    # Test required dependencies
    for module, package in dependencies:
        try:
            importlib.import_module(module)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - MISSING")
            all_good = False
    
    # Test optional dependencies
    print("\nOptional dependencies:")
    for module, package in optional_dependencies:
        try:
            importlib.import_module(module)
            print(f"✓ {package}")
        except ImportError:
            print(f"⚠ {package} - Optional (some features may not work)")
    
    return all_good

def test_database():
    """Test database functionality"""
    print("\nTesting database...")
    
    try:
        from database import DatabaseManager
        db = DatabaseManager("test_database.db")
        
        # Test basic operations
        cameras = db.get_cameras()
        print(f"✓ Database initialized - {len(cameras)} cameras found")
        
        # Cleanup test database
        if os.path.exists("test_database.db"):
            os.remove("test_database.db")
        
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_camera_manager():
    """Test camera manager"""
    print("\nTesting camera manager...")
    
    try:
        from camera_manager import CameraManager
        cm = CameraManager()
        print("✓ Camera manager initialized")
        return True
        
    except Exception as e:
        print(f"✗ Camera manager test failed: {e}")
        return False

def test_motion_detector():
    """Test motion detection"""
    print("\nTesting motion detector...")
    
    try:
        from motion_detector import MotionManager
        mm = MotionManager()
        print("✓ Motion detector initialized")
        return True
        
    except Exception as e:
        print(f"✗ Motion detector test failed: {e}")
        return False

def test_user_manager():
    """Test user management"""
    print("\nTesting user manager...")
    
    try:
        from user_manager import UserManager
        um = UserManager()
        
        # Test authentication with default admin user
        success, message = um.authenticate("admin", "admin123")
        if success:
            print("✓ User authentication working")
            um.logout()
        else:
            print(f"⚠ Default admin login failed: {message}")
        
        return True
        
    except Exception as e:
        print(f"✗ User manager test failed: {e}")
        return False

def test_settings():
    """Test settings management"""
    print("\nTesting settings...")
    
    try:
        from settings import SettingsManager
        sm = SettingsManager("test_settings.json")
        
        # Test basic operations
        theme = sm.get_setting('theme')
        print(f"✓ Settings loaded - Theme: {theme}")
        
        # Cleanup
        if os.path.exists("test_settings.json"):
            os.remove("test_settings.json")
        
        return True
        
    except Exception as e:
        print(f"✗ Settings test failed: {e}")
        return False

def test_ui_components():
    """Test UI components"""
    print("\nTesting UI components...")
    
    try:
        # Test PyQt5 application creation
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # Create minimal application (don't show)
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✓ PyQt5 application can be created")
        
        # Test main window import
        from ui.main_window import MainWindow
        print("✓ Main window class can be imported")
        
        # Test login dialog import
        from ui.login_dialog import LoginDialog
        print("✓ Login dialog class can be imported")
        
        return True
        
    except Exception as e:
        print(f"✗ UI components test failed: {e}")
        return False

def test_directories():
    """Test directory structure"""
    print("\nTesting directories...")
    
    required_dirs = ["ui", "videos", "logs"]
    optional_dirs = ["snapshots", "sounds", "resources"]
    
    all_good = True
    
    for dir_name in required_dirs:
        if os.path.exists(dir_name):
            print(f"✓ {dir_name}/ directory exists")
        else:
            print(f"✗ {dir_name}/ directory missing")
            all_good = False
    
    for dir_name in optional_dirs:
        if os.path.exists(dir_name):
            print(f"✓ {dir_name}/ directory exists")
        else:
            print(f"⚠ {dir_name}/ directory missing (will be created)")
    
    return all_good

def test_opencv_camera():
    """Test OpenCV camera functionality"""
    print("\nTesting OpenCV camera support...")
    
    try:
        import cv2
        
        # Test basic OpenCV functionality
        test_image = cv2.imread("test_image.jpg")  # This will fail but that's OK
        
        # Test video capture creation (without actually opening)
        cap = cv2.VideoCapture()
        print("✓ OpenCV video capture can be created")
        
        # Test background subtractor
        bg_subtractor = cv2.createBackgroundSubtractorMOG2()
        print("✓ Background subtractor available")
        
        return True
        
    except Exception as e:
        print(f"✗ OpenCV test failed: {e}")
        return False

def create_test_report(results):
    """Create test report"""
    print("\n" + "=" * 60)
    print("SMARTPSS INSTALLATION TEST REPORT")
    print("=" * 60)
    print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python Version: {sys.version}")
    print(f"Platform: {sys.platform}")
    print()
    
    passed = sum(results.values())
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    print()
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<30} {status}")
    
    print("\n" + "=" * 60)
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! SmartPSS is ready to run.")
        print("You can now start the application with: python main.py")
    else:
        print("⚠️  SOME TESTS FAILED!")
        print("Please install missing dependencies before running SmartPSS.")
        print("Run: pip install -r requirements.txt")
    
    print("=" * 60)
    
    return passed == total

def main():
    """Run all tests"""
    print("SmartPSS Installation Test")
    print("=" * 60)
    
    tests = [
        ("Python Version", test_python_version),
        ("Dependencies", test_dependencies),
        ("Database", test_database),
        ("Camera Manager", test_camera_manager),
        ("Motion Detector", test_motion_detector),
        ("User Manager", test_user_manager),
        ("Settings", test_settings),
        ("UI Components", test_ui_components),
        ("Directories", test_directories),
        ("OpenCV Camera", test_opencv_camera),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results[test_name] = False
    
    # Create report
    success = create_test_report(results)
    
    return 0 if success else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Test script error: {e}")
        sys.exit(1)
