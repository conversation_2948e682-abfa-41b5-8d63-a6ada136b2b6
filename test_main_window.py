#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test main window directly
اختبار النافذة الرئيسية مباشرة
"""

import sys
import os

print("🧪 اختبار النافذة الرئيسية...")

try:
    print("1. اختبار PyQt5...")
    from PyQt5.QtWidgets import QApplication
    print("✅ PyQt5 يعمل")
    
    print("2. اختبار استيراد النافذة الرئيسية...")
    from ui.main_window import MainWindow
    print("✅ استيراد النافذة الرئيسية يعمل")
    
    print("3. اختبار إنشاء التطبيق...")
    app = QApplication(sys.argv)
    print("✅ إنشاء التطبيق يعمل")
    
    print("4. اختبار إنشاء النافذة الرئيسية...")
    window = MainWindow()
    print("✅ إنشاء النافذة الرئيسية يعمل")
    
    print("5. اختبار عرض النافذة...")
    window.show()
    print("✅ عرض النافذة يعمل")
    
    print("🎉 جميع الاختبارات نجحت!")
    print("النافذة ستظهر الآن...")
    
    # تشغيل التطبيق
    sys.exit(app.exec_())
    
except Exception as e:
    print(f"❌ خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()
