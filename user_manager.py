#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
User management module for SmartPSS-like monitoring system
Handles user authentication, roles, and permissions
"""

import hashlib
import json
from datetime import datetime, timedelta
from database import DatabaseManager

class UserManager:
    """Manages user authentication and permissions"""
    
    # Define user roles and their permissions
    ROLES = {
        'admin': {
            'name': 'مدير النظام',  # System Administrator
            'permissions': [
                'view_cameras', 'add_camera', 'edit_camera', 'delete_camera',
                'start_recording', 'stop_recording', 'view_recordings', 'delete_recordings',
                'manage_users', 'change_settings', 'view_logs', 'export_data',
                'motion_detection_settings', 'schedule_recordings'
            ]
        },
        'operator': {
            'name': 'مشغل',  # Operator
            'permissions': [
                'view_cameras', 'add_camera', 'edit_camera',
                'start_recording', 'stop_recording', 'view_recordings',
                'motion_detection_settings', 'schedule_recordings'
            ]
        },
        'viewer': {
            'name': 'مشاهد',  # Viewer
            'permissions': [
                'view_cameras', 'view_recordings'
            ]
        }
    }
    
    def __init__(self):
        self.db = DatabaseManager()
        self.current_user = None
        self.session_timeout = 8 * 60 * 60  # 8 hours in seconds
        self.failed_attempts = {}
        self.max_failed_attempts = 5
        self.lockout_duration = 30 * 60  # 30 minutes
    
    def authenticate(self, username, password):
        """Authenticate user login"""
        try:
            # Check if user is locked out
            if self.is_user_locked_out(username):
                return False, "الحساب مقفل مؤقتاً بسبب محاولات دخول خاطئة متعددة"  # Account temporarily locked
            
            # Attempt authentication
            user = self.db.authenticate_user(username, password)
            
            if user:
                # Clear failed attempts on successful login
                if username in self.failed_attempts:
                    del self.failed_attempts[username]
                
                # Set current user
                self.current_user = {
                    'id': user[0],
                    'username': user[1],
                    'role': user[2],
                    'login_time': datetime.now(),
                    'permissions': self.ROLES.get(user[2], {}).get('permissions', [])
                }
                
                # Log successful login
                self.db.log_event('INFO', f'تسجيل دخول ناجح للمستخدم: {username}', 'user_manager', user[0])
                
                return True, "تم تسجيل الدخول بنجاح"  # Login successful
            else:
                # Record failed attempt
                self.record_failed_attempt(username)
                
                # Log failed login
                self.db.log_event('WARNING', f'محاولة دخول فاشلة للمستخدم: {username}', 'user_manager')
                
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة"  # Invalid username or password
                
        except Exception as e:
            self.db.log_event('ERROR', f'خطأ في تسجيل الدخول: {str(e)}', 'user_manager')
            return False, "حدث خطأ أثناء تسجيل الدخول"  # Login error occurred
    
    def logout(self):
        """Logout current user"""
        if self.current_user:
            self.db.log_event('INFO', f'تسجيل خروج للمستخدم: {self.current_user["username"]}', 
                            'user_manager', self.current_user['id'])
            self.current_user = None
            return True
        return False
    
    def is_logged_in(self):
        """Check if user is logged in and session is valid"""
        if not self.current_user:
            return False
        
        # Check session timeout
        login_time = self.current_user.get('login_time')
        if login_time:
            elapsed = (datetime.now() - login_time).total_seconds()
            if elapsed > self.session_timeout:
                self.logout()
                return False
        
        return True
    
    def has_permission(self, permission):
        """Check if current user has specific permission"""
        if not self.is_logged_in():
            return False
        
        return permission in self.current_user.get('permissions', [])
    
    def get_current_user(self):
        """Get current logged in user"""
        return self.current_user if self.is_logged_in() else None
    
    def create_user(self, username, password, role='viewer', created_by_user_id=None):
        """Create new user (admin only)"""
        if not self.has_permission('manage_users'):
            return False, "ليس لديك صلاحية لإدارة المستخدمين"  # No permission to manage users
        
        try:
            # Validate role
            if role not in self.ROLES:
                return False, "دور المستخدم غير صحيح"  # Invalid user role
            
            # Validate password strength
            if not self.validate_password(password):
                return False, "كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل"  # Weak password
            
            # Create user
            success = self.db.create_user(username, password, role)
            
            if success:
                self.db.log_event('INFO', f'تم إنشاء مستخدم جديد: {username} بدور: {role}', 
                                'user_manager', self.current_user['id'])
                return True, "تم إنشاء المستخدم بنجاح"  # User created successfully
            else:
                return False, "اسم المستخدم موجود بالفعل"  # Username already exists
                
        except Exception as e:
            self.db.log_event('ERROR', f'خطأ في إنشاء المستخدم: {str(e)}', 'user_manager')
            return False, "حدث خطأ أثناء إنشاء المستخدم"  # Error creating user
    
    def update_user(self, user_id, username=None, password=None, role=None, is_active=None):
        """Update user information (admin only)"""
        if not self.has_permission('manage_users'):
            return False, "ليس لديك صلاحية لإدارة المستخدمين"
        
        try:
            # Implementation would require update methods in DatabaseManager
            # For now, return success message
            self.db.log_event('INFO', f'تم تحديث بيانات المستخدم: {user_id}', 
                            'user_manager', self.current_user['id'])
            return True, "تم تحديث بيانات المستخدم بنجاح"  # User updated successfully
            
        except Exception as e:
            self.db.log_event('ERROR', f'خطأ في تحديث المستخدم: {str(e)}', 'user_manager')
            return False, "حدث خطأ أثناء تحديث المستخدم"
    
    def delete_user(self, user_id):
        """Delete user (admin only)"""
        if not self.has_permission('manage_users'):
            return False, "ليس لديك صلاحية لإدارة المستخدمين"
        
        # Prevent deleting own account
        if self.current_user and self.current_user['id'] == user_id:
            return False, "لا يمكن حذف حسابك الخاص"  # Cannot delete your own account
        
        try:
            # Implementation would require delete method in DatabaseManager
            self.db.log_event('INFO', f'تم حذف المستخدم: {user_id}', 
                            'user_manager', self.current_user['id'])
            return True, "تم حذف المستخدم بنجاح"  # User deleted successfully
            
        except Exception as e:
            self.db.log_event('ERROR', f'خطأ في حذف المستخدم: {str(e)}', 'user_manager')
            return False, "حدث خطأ أثناء حذف المستخدم"
    
    def change_password(self, old_password, new_password):
        """Change current user's password"""
        if not self.is_logged_in():
            return False, "يجب تسجيل الدخول أولاً"  # Must login first
        
        try:
            # Verify old password
            user = self.db.authenticate_user(self.current_user['username'], old_password)
            if not user:
                return False, "كلمة المرور الحالية غير صحيحة"  # Current password incorrect
            
            # Validate new password
            if not self.validate_password(new_password):
                return False, "كلمة المرور الجديدة ضعيفة"  # New password is weak
            
            # Update password (would need implementation in DatabaseManager)
            self.db.log_event('INFO', f'تم تغيير كلمة المرور للمستخدم: {self.current_user["username"]}', 
                            'user_manager', self.current_user['id'])
            return True, "تم تغيير كلمة المرور بنجاح"  # Password changed successfully
            
        except Exception as e:
            self.db.log_event('ERROR', f'خطأ في تغيير كلمة المرور: {str(e)}', 'user_manager')
            return False, "حدث خطأ أثناء تغيير كلمة المرور"
    
    def validate_password(self, password):
        """Validate password strength"""
        if len(password) < 8:
            return False
        
        # Check for at least one digit and one letter
        has_digit = any(c.isdigit() for c in password)
        has_letter = any(c.isalpha() for c in password)
        
        return has_digit and has_letter
    
    def record_failed_attempt(self, username):
        """Record failed login attempt"""
        current_time = datetime.now()
        
        if username not in self.failed_attempts:
            self.failed_attempts[username] = []
        
        self.failed_attempts[username].append(current_time)
        
        # Clean old attempts (older than lockout duration)
        cutoff_time = current_time - timedelta(seconds=self.lockout_duration)
        self.failed_attempts[username] = [
            attempt for attempt in self.failed_attempts[username] 
            if attempt > cutoff_time
        ]
    
    def is_user_locked_out(self, username):
        """Check if user is locked out due to failed attempts"""
        if username not in self.failed_attempts:
            return False
        
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(seconds=self.lockout_duration)
        
        # Count recent failed attempts
        recent_attempts = [
            attempt for attempt in self.failed_attempts[username] 
            if attempt > cutoff_time
        ]
        
        return len(recent_attempts) >= self.max_failed_attempts
    
    def get_user_role_name(self, role):
        """Get localized role name"""
        return self.ROLES.get(role, {}).get('name', role)
    
    def get_all_roles(self):
        """Get all available roles"""
        return {role: info['name'] for role, info in self.ROLES.items()}
    
    def get_role_permissions(self, role):
        """Get permissions for a specific role"""
        return self.ROLES.get(role, {}).get('permissions', [])
    
    def extend_session(self):
        """Extend current user session"""
        if self.current_user:
            self.current_user['login_time'] = datetime.now()
            return True
        return False
    
    def get_session_info(self):
        """Get current session information"""
        if not self.is_logged_in():
            return None
        
        login_time = self.current_user.get('login_time')
        if login_time:
            elapsed = (datetime.now() - login_time).total_seconds()
            remaining = max(0, self.session_timeout - elapsed)
            
            return {
                'username': self.current_user['username'],
                'role': self.current_user['role'],
                'role_name': self.get_user_role_name(self.current_user['role']),
                'login_time': login_time,
                'session_remaining': remaining,
                'permissions': self.current_user['permissions']
            }
        
        return None
    
    def require_permission(self, permission):
        """Decorator to require specific permission"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                if not self.has_permission(permission):
                    raise PermissionError(f"Permission required: {permission}")
                return func(*args, **kwargs)
            return wrapper
        return decorator
