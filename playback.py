#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Video playback module for SmartPSS-like monitoring system
Handles video playback with timeline controls and frame navigation
"""

import cv2
import os
import threading
import time
from datetime import datetime, timedelta
from PyQt5.QtCore import QThread, pyqtSignal, QTimer
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QSlider, QLabel
from PyQt5.QtCore import Qt
import numpy as np
from database import DatabaseManager

class VideoPlayer(QThread):
    """Video player thread for playback control"""
    
    frame_ready = pyqtSignal(np.ndarray, int)  # frame, frame_number
    position_changed = pyqtSignal(int, int)    # current_frame, total_frames
    playback_finished = pyqtSignal()
    playback_error = pyqtSignal(str)
    
    def __init__(self, video_path):
        super().__init__()
        self.video_path = video_path
        self.cap = None
        self.playing = False
        self.paused = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.seek_frame = -1
        self.playback_speed = 1.0
        
        # Initialize video capture
        self.init_video()
    
    def init_video(self):
        """Initialize video capture"""
        try:
            if not os.path.exists(self.video_path):
                raise Exception(f"Video file not found: {self.video_path}")
            
            self.cap = cv2.VideoCapture(self.video_path)
            if not self.cap.isOpened():
                raise Exception("Failed to open video file")
            
            # Get video properties
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            if self.fps <= 0:
                self.fps = 30  # Default FPS
            
            return True
            
        except Exception as e:
            self.playback_error.emit(str(e))
            return False
    
    def play(self):
        """Start playback"""
        if self.cap and self.cap.isOpened():
            self.playing = True
            self.paused = False
            if not self.isRunning():
                self.start()
    
    def pause(self):
        """Pause playback"""
        self.paused = not self.paused
    
    def stop(self):
        """Stop playback"""
        self.playing = False
        self.paused = False
    
    def seek(self, frame_number):
        """Seek to specific frame"""
        if 0 <= frame_number < self.total_frames:
            self.seek_frame = frame_number
    
    def set_speed(self, speed):
        """Set playback speed (0.25x to 4x)"""
        self.playback_speed = max(0.25, min(4.0, speed))
    
    def run(self):
        """Main playback loop"""
        if not self.cap or not self.cap.isOpened():
            return
        
        frame_delay = 1.0 / (self.fps * self.playback_speed)
        
        while self.playing:
            try:
                # Handle seek request
                if self.seek_frame >= 0:
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.seek_frame)
                    self.current_frame = self.seek_frame
                    self.seek_frame = -1
                
                # Skip if paused
                if self.paused:
                    time.sleep(0.1)
                    continue
                
                # Read frame
                ret, frame = self.cap.read()
                if not ret:
                    self.playback_finished.emit()
                    break
                
                # Emit frame and position
                self.frame_ready.emit(frame, self.current_frame)
                self.position_changed.emit(self.current_frame, self.total_frames)
                
                self.current_frame += 1
                
                # Control playback speed
                time.sleep(frame_delay)
                
            except Exception as e:
                self.playback_error.emit(str(e))
                break
        
        self.playing = False
    
    def get_frame_at_position(self, frame_number):
        """Get specific frame without affecting playback"""
        if not self.cap or not self.cap.isOpened():
            return None
        
        try:
            # Save current position
            current_pos = self.cap.get(cv2.CAP_PROP_POS_FRAMES)
            
            # Seek to desired frame
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = self.cap.read()
            
            # Restore position
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, current_pos)
            
            return frame if ret else None
            
        except Exception:
            return None
    
    def get_video_info(self):
        """Get video information"""
        if not self.cap or not self.cap.isOpened():
            return None
        
        return {
            'total_frames': self.total_frames,
            'fps': self.fps,
            'duration': self.total_frames / self.fps if self.fps > 0 else 0,
            'width': int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            'height': int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        }
    
    def cleanup(self):
        """Clean up resources"""
        self.stop()
        if self.cap:
            self.cap.release()
            self.cap = None

class PlaybackManager:
    """Manages video playback operations"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.active_players = {}
        self.recordings_cache = {}
    
    def create_player(self, video_path, player_id=None):
        """Create a new video player"""
        if not os.path.exists(video_path):
            return None, "Video file not found"
        
        try:
            if player_id is None:
                player_id = len(self.active_players)
            
            player = VideoPlayer(video_path)
            self.active_players[player_id] = player
            
            return player, "Player created successfully"
            
        except Exception as e:
            return None, f"Failed to create player: {e}"
    
    def get_player(self, player_id):
        """Get existing player"""
        return self.active_players.get(player_id)
    
    def remove_player(self, player_id):
        """Remove and cleanup player"""
        if player_id in self.active_players:
            player = self.active_players[player_id]
            player.cleanup()
            del self.active_players[player_id]
    
    def cleanup_all_players(self):
        """Clean up all players"""
        for player_id in list(self.active_players.keys()):
            self.remove_player(player_id)
    
    def get_recordings_by_date(self, camera_id, date):
        """Get recordings for specific camera and date"""
        try:
            # Convert date to datetime range
            start_date = datetime.combine(date, datetime.min.time())
            end_date = start_date + timedelta(days=1)
            
            # Query database (simplified - would need date filtering in DatabaseManager)
            recordings = self.db.get_recordings(camera_id)
            
            # Filter by date (basic implementation)
            filtered_recordings = []
            for recording in recordings:
                # Parse start_time from database
                try:
                    record_time = datetime.fromisoformat(recording[4])  # start_time column
                    if start_date <= record_time < end_date:
                        filtered_recordings.append(recording)
                except:
                    continue
            
            return filtered_recordings
            
        except Exception as e:
            print(f"Error getting recordings by date: {e}")
            return []
    
    def get_recordings_by_time_range(self, camera_id, start_time, end_time):
        """Get recordings within time range"""
        try:
            recordings = self.db.get_recordings(camera_id)
            
            filtered_recordings = []
            for recording in recordings:
                try:
                    record_start = datetime.fromisoformat(recording[4])  # start_time
                    record_end = datetime.fromisoformat(recording[5]) if recording[5] else record_start  # end_time
                    
                    # Check if recording overlaps with requested time range
                    if (record_start <= end_time and record_end >= start_time):
                        filtered_recordings.append(recording)
                except:
                    continue
            
            return filtered_recordings
            
        except Exception as e:
            print(f"Error getting recordings by time range: {e}")
            return []
    
    def create_timeline_data(self, camera_id, date):
        """Create timeline data for a specific date"""
        recordings = self.get_recordings_by_date(camera_id, date)
        
        timeline_data = []
        for recording in recordings:
            try:
                start_time = datetime.fromisoformat(recording[4])
                end_time = datetime.fromisoformat(recording[5]) if recording[5] else start_time
                duration = recording[6] if recording[6] else 0
                
                timeline_data.append({
                    'id': recording[0],
                    'filename': recording[2],
                    'file_path': recording[3],
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': duration,
                    'recording_type': recording[8],
                    'motion_triggered': recording[9]
                })
            except:
                continue
        
        # Sort by start time
        timeline_data.sort(key=lambda x: x['start_time'])
        
        return timeline_data
    
    def export_video_segment(self, video_path, start_time, end_time, output_path):
        """Export a segment of video"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return False, "Cannot open source video"
            
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # Calculate frame positions
            start_frame = int(start_time * fps)
            end_frame = int(end_time * fps)
            
            # Set up video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            # Seek to start frame
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            # Copy frames
            current_frame = start_frame
            while current_frame <= end_frame:
                ret, frame = cap.read()
                if not ret:
                    break
                
                out.write(frame)
                current_frame += 1
            
            # Cleanup
            cap.release()
            out.release()
            
            return True, "Video segment exported successfully"
            
        except Exception as e:
            return False, f"Export failed: {e}"
    
    def generate_thumbnail(self, video_path, timestamp=None, output_path=None):
        """Generate thumbnail from video"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None
            
            # Seek to timestamp or middle of video
            if timestamp is not None:
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_number = int(timestamp * fps)
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            else:
                # Seek to middle
                total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                cap.set(cv2.CAP_PROP_POS_FRAMES, total_frames // 2)
            
            # Read frame
            ret, frame = cap.read()
            cap.release()
            
            if not ret:
                return None
            
            # Resize for thumbnail
            thumbnail = cv2.resize(frame, (160, 120))
            
            # Save if output path provided
            if output_path:
                cv2.imwrite(output_path, thumbnail)
            
            return thumbnail
            
        except Exception as e:
            print(f"Thumbnail generation error: {e}")
            return None
    
    def get_video_metadata(self, video_path):
        """Get detailed video metadata"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None
            
            metadata = {
                'file_path': video_path,
                'file_size': os.path.getsize(video_path),
                'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                'fps': cap.get(cv2.CAP_PROP_FPS),
                'total_frames': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
                'duration': 0,
                'codec': 'Unknown',
                'created_date': datetime.fromtimestamp(os.path.getctime(video_path)),
                'modified_date': datetime.fromtimestamp(os.path.getmtime(video_path))
            }
            
            # Calculate duration
            if metadata['fps'] > 0:
                metadata['duration'] = metadata['total_frames'] / metadata['fps']
            
            cap.release()
            return metadata
            
        except Exception as e:
            print(f"Metadata extraction error: {e}")
            return None
    
    def search_recordings(self, search_criteria):
        """Search recordings based on criteria"""
        try:
            all_recordings = self.db.get_recordings(limit=1000)
            
            filtered_recordings = []
            for recording in all_recordings:
                match = True
                
                # Filter by camera_id
                if 'camera_id' in search_criteria:
                    if recording[1] != search_criteria['camera_id']:
                        match = False
                
                # Filter by date range
                if 'start_date' in search_criteria and 'end_date' in search_criteria:
                    try:
                        record_time = datetime.fromisoformat(recording[4])
                        if not (search_criteria['start_date'] <= record_time <= search_criteria['end_date']):
                            match = False
                    except:
                        match = False
                
                # Filter by recording type
                if 'recording_type' in search_criteria:
                    if recording[8] != search_criteria['recording_type']:
                        match = False
                
                # Filter by motion triggered
                if 'motion_triggered' in search_criteria:
                    if recording[9] != search_criteria['motion_triggered']:
                        match = False
                
                if match:
                    filtered_recordings.append(recording)
            
            return filtered_recordings
            
        except Exception as e:
            print(f"Search error: {e}")
            return []
