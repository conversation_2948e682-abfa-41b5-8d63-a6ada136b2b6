#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Alert system module for SmartPSS-like monitoring system
Handles motion alerts, sound notifications, and email alerts
"""

import os
import threading
import time
import smtplib
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.image import MIMEImage
from PyQt5.QtCore import QThread, pyqtSignal, QTimer
from PyQt5.QtWidgets import QSystemTrayIcon, QMenu, QAction
from PyQt5.QtGui import QIcon
import cv2

try:
    from playsound import playsound
    SOUND_AVAILABLE = True
except ImportError:
    SOUND_AVAILABLE = False
    print("Warning: playsound not available. Sound alerts disabled.")

from database import DatabaseManager
from settings import SettingsManager

class AlertManager(QThread):
    """Manages all types of alerts and notifications"""
    
    alert_triggered = pyqtSignal(str, str, dict)  # alert_type, message, data
    
    def __init__(self):
        super().__init__()
        self.db = DatabaseManager()
        self.settings = SettingsManager()
        self.running = False
        self.alert_queue = []
        self.alert_lock = threading.Lock()
        
        # System tray icon
        self.tray_icon = None
        self.setup_system_tray()
        
        # Email configuration
        self.email_config = {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'username': '',
            'password': '',
            'from_email': ''
        }
        
        # Sound files
        self.sound_files = {
            'motion': 'sounds/motion_alert.wav',
            'connection_lost': 'sounds/connection_lost.wav',
            'connection_restored': 'sounds/connection_restored.wav',
            'recording_started': 'sounds/recording_started.wav',
            'recording_stopped': 'sounds/recording_stopped.wav'
        }
        
        # Create sounds directory
        os.makedirs('sounds', exist_ok=True)
    
    def setup_system_tray(self):
        """Setup system tray icon"""
        try:
            if QSystemTrayIcon.isSystemTrayAvailable():
                self.tray_icon = QSystemTrayIcon()
                
                # Set icon (you would need to provide an icon file)
                # self.tray_icon.setIcon(QIcon('icons/app_icon.png'))
                
                # Create context menu
                tray_menu = QMenu()
                
                show_action = QAction("إظهار التطبيق", None)  # Show Application
                show_action.triggered.connect(self.show_main_window)
                tray_menu.addAction(show_action)
                
                tray_menu.addSeparator()
                
                quit_action = QAction("إنهاء", None)  # Quit
                quit_action.triggered.connect(self.quit_application)
                tray_menu.addAction(quit_action)
                
                self.tray_icon.setContextMenu(tray_menu)
                self.tray_icon.show()
                
                # Connect double-click to show window
                self.tray_icon.activated.connect(self.tray_icon_activated)
                
        except Exception as e:
            print(f"System tray setup error: {e}")
    
    def tray_icon_activated(self, reason):
        """Handle tray icon activation"""
        if reason == QSystemTrayIcon.DoubleClick:
            self.show_main_window()
    
    def show_main_window(self):
        """Show main application window"""
        # This would be connected to the main window show method
        pass
    
    def quit_application(self):
        """Quit application"""
        # This would be connected to the application quit method
        pass
    
    def start(self):
        """Start alert manager"""
        self.running = True
        super().start()
    
    def stop(self):
        """Stop alert manager"""
        self.running = False
        self.wait()
    
    def run(self):
        """Main alert processing loop"""
        while self.running:
            try:
                with self.alert_lock:
                    if self.alert_queue:
                        alert = self.alert_queue.pop(0)
                        self.process_alert(alert)
                
                time.sleep(0.1)  # Check every 100ms
                
            except Exception as e:
                print(f"Alert processing error: {e}")
    
    def add_alert(self, alert_type, message, data=None):
        """Add alert to processing queue"""
        alert = {
            'type': alert_type,
            'message': message,
            'data': data or {},
            'timestamp': datetime.now()
        }
        
        with self.alert_lock:
            self.alert_queue.append(alert)
    
    def process_alert(self, alert):
        """Process individual alert"""
        try:
            alert_type = alert['type']
            message = alert['message']
            data = alert['data']
            
            # Log alert
            self.db.log_event('ALERT', f"{alert_type}: {message}", 'alerts')
            
            # Emit signal for UI updates
            self.alert_triggered.emit(alert_type, message, data)
            
            # Play sound if enabled
            if self.settings.get_setting('alert_sound_enabled'):
                self.play_alert_sound(alert_type)
            
            # Show system tray notification
            self.show_tray_notification(alert_type, message)
            
            # Send email if enabled
            if self.settings.get_setting('email_alerts_enabled'):
                self.send_email_alert(alert_type, message, data)
            
        except Exception as e:
            print(f"Alert processing error: {e}")
    
    def play_alert_sound(self, alert_type):
        """Play alert sound"""
        if not SOUND_AVAILABLE:
            return
        
        try:
            # Get custom sound file or use default
            sound_file = self.settings.get_setting('alert_sound_file')
            if sound_file == 'default' or not os.path.exists(sound_file):
                sound_file = self.sound_files.get(alert_type, self.sound_files.get('motion'))
            
            if os.path.exists(sound_file):
                # Play sound in separate thread to avoid blocking
                threading.Thread(target=lambda: playsound(sound_file), daemon=True).start()
            else:
                # Generate beep sound if no file available
                self.generate_beep_sound()
                
        except Exception as e:
            print(f"Sound playback error: {e}")
    
    def generate_beep_sound(self):
        """Generate system beep sound"""
        try:
            import winsound
            winsound.Beep(1000, 500)  # 1000 Hz for 500ms
        except ImportError:
            # For non-Windows systems
            try:
                os.system('echo -e "\a"')
            except:
                pass
    
    def show_tray_notification(self, alert_type, message):
        """Show system tray notification"""
        if self.tray_icon and self.tray_icon.isVisible():
            try:
                title = self.get_alert_title(alert_type)
                self.tray_icon.showMessage(title, message, QSystemTrayIcon.Information, 5000)
            except Exception as e:
                print(f"Tray notification error: {e}")
    
    def get_alert_title(self, alert_type):
        """Get localized alert title"""
        titles = {
            'motion_detected': 'تم اكتشاف حركة',  # Motion Detected
            'connection_lost': 'انقطع الاتصال',  # Connection Lost
            'connection_restored': 'تم استعادة الاتصال',  # Connection Restored
            'recording_started': 'بدأ التسجيل',  # Recording Started
            'recording_stopped': 'توقف التسجيل',  # Recording Stopped
            'storage_full': 'امتلأ التخزين',  # Storage Full
            'system_error': 'خطأ في النظام'  # System Error
        }
        return titles.get(alert_type, 'تنبيه')  # Alert
    
    def send_email_alert(self, alert_type, message, data):
        """Send email alert"""
        try:
            email_address = self.settings.get_setting('email_address')
            if not email_address:
                return
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = self.email_config['from_email']
            msg['To'] = email_address
            msg['Subject'] = f"SmartPSS Alert: {self.get_alert_title(alert_type)}"
            
            # Email body
            body = self.create_email_body(alert_type, message, data)
            msg.attach(MIMEText(body, 'html', 'utf-8'))
            
            # Attach snapshot if available
            if 'snapshot_path' in data and os.path.exists(data['snapshot_path']):
                with open(data['snapshot_path'], 'rb') as f:
                    img_data = f.read()
                    image = MIMEImage(img_data)
                    image.add_header('Content-Disposition', 'attachment', filename='snapshot.jpg')
                    msg.attach(image)
            
            # Send email
            self.send_email(msg)
            
        except Exception as e:
            print(f"Email alert error: {e}")
    
    def create_email_body(self, alert_type, message, data):
        """Create HTML email body"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        camera_name = data.get('camera_name', 'غير معروف')  # Unknown
        
        html_body = f"""
        <html>
        <body dir="rtl" style="font-family: Arial, sans-serif;">
            <h2 style="color: #d32f2f;">تنبيه من نظام SmartPSS</h2>
            <p><strong>نوع التنبيه:</strong> {self.get_alert_title(alert_type)}</p>
            <p><strong>الرسالة:</strong> {message}</p>
            <p><strong>الكاميرا:</strong> {camera_name}</p>
            <p><strong>الوقت:</strong> {timestamp}</p>
            
            {self.get_alert_specific_content(alert_type, data)}
            
            <hr>
            <p style="font-size: 12px; color: #666;">
                هذا تنبيه تلقائي من نظام SmartPSS للمراقبة
            </p>
        </body>
        </html>
        """
        return html_body
    
    def get_alert_specific_content(self, alert_type, data):
        """Get alert-specific content for email"""
        if alert_type == 'motion_detected':
            confidence = data.get('confidence', 0)
            return f"<p><strong>مستوى الثقة:</strong> {confidence:.1f}%</p>"
        elif alert_type == 'recording_started':
            duration = data.get('duration_minutes', 'غير محدد')
            return f"<p><strong>مدة التسجيل المتوقعة:</strong> {duration} دقيقة</p>"
        elif alert_type == 'recording_stopped':
            duration = data.get('duration_seconds', 0)
            file_size = data.get('file_size', 0)
            return f"""
            <p><strong>مدة التسجيل:</strong> {duration} ثانية</p>
            <p><strong>حجم الملف:</strong> {file_size / (1024*1024):.1f} ميجابايت</p>
            """
        return ""
    
    def send_email(self, msg):
        """Send email using SMTP"""
        try:
            server = smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port'])
            server.starttls()
            server.login(self.email_config['username'], self.email_config['password'])
            server.send_message(msg)
            server.quit()
            
        except Exception as e:
            print(f"SMTP error: {e}")
    
    def configure_email(self, smtp_server, smtp_port, username, password, from_email):
        """Configure email settings"""
        self.email_config.update({
            'smtp_server': smtp_server,
            'smtp_port': smtp_port,
            'username': username,
            'password': password,
            'from_email': from_email
        })
    
    def test_email_configuration(self):
        """Test email configuration"""
        try:
            test_msg = MIMEText("This is a test message from SmartPSS", 'plain', 'utf-8')
            test_msg['Subject'] = "SmartPSS Email Test"
            test_msg['From'] = self.email_config['from_email']
            test_msg['To'] = self.settings.get_setting('email_address')
            
            self.send_email(test_msg)
            return True, "تم إرسال رسالة الاختبار بنجاح"  # Test email sent successfully
            
        except Exception as e:
            return False, f"فشل في إرسال رسالة الاختبار: {str(e)}"  # Failed to send test email
    
    # Alert trigger methods
    def motion_detected(self, camera_id, camera_name, confidence, snapshot_path=None):
        """Trigger motion detection alert"""
        message = f"تم اكتشاف حركة في الكاميرا: {camera_name}"  # Motion detected in camera
        data = {
            'camera_id': camera_id,
            'camera_name': camera_name,
            'confidence': confidence,
            'snapshot_path': snapshot_path
        }
        self.add_alert('motion_detected', message, data)
    
    def connection_lost(self, camera_id, camera_name):
        """Trigger connection lost alert"""
        message = f"انقطع الاتصال مع الكاميرا: {camera_name}"  # Connection lost with camera
        data = {
            'camera_id': camera_id,
            'camera_name': camera_name
        }
        self.add_alert('connection_lost', message, data)
    
    def connection_restored(self, camera_id, camera_name):
        """Trigger connection restored alert"""
        message = f"تم استعادة الاتصال مع الكاميرا: {camera_name}"  # Connection restored with camera
        data = {
            'camera_id': camera_id,
            'camera_name': camera_name
        }
        self.add_alert('connection_restored', message, data)
    
    def recording_started(self, camera_id, camera_name, filename, duration_minutes=None):
        """Trigger recording started alert"""
        message = f"بدأ تسجيل الكاميرا: {camera_name}"  # Recording started for camera
        data = {
            'camera_id': camera_id,
            'camera_name': camera_name,
            'filename': filename,
            'duration_minutes': duration_minutes
        }
        self.add_alert('recording_started', message, data)
    
    def recording_stopped(self, camera_id, camera_name, filename, duration_seconds, file_size):
        """Trigger recording stopped alert"""
        message = f"توقف تسجيل الكاميرا: {camera_name}"  # Recording stopped for camera
        data = {
            'camera_id': camera_id,
            'camera_name': camera_name,
            'filename': filename,
            'duration_seconds': duration_seconds,
            'file_size': file_size
        }
        self.add_alert('recording_stopped', message, data)
    
    def storage_full(self, available_space_mb):
        """Trigger storage full alert"""
        message = f"مساحة التخزين منخفضة: {available_space_mb} ميجابايت متبقية"  # Storage space low
        data = {
            'available_space_mb': available_space_mb
        }
        self.add_alert('storage_full', message, data)
    
    def system_error(self, error_message, module=None):
        """Trigger system error alert"""
        message = f"خطأ في النظام: {error_message}"  # System error
        data = {
            'error_message': error_message,
            'module': module
        }
        self.add_alert('system_error', message, data)
