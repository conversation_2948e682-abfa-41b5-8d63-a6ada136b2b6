#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Camera configuration dialog for SmartPSS-like monitoring system
Add/Edit camera settings with RTSP configuration
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                           QLineEdit, QPushButton, QLabel, QCheckBox, 
                           QMessageBox, QFrame, QGroupBox, QComboBox,
                           QSpinBox, QTextEdit, QProgressBar, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPixmap

class CameraTestThread(QThread):
    """Thread for testing camera connection"""
    
    test_result = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, rtsp_url):
        super().__init__()
        self.rtsp_url = rtsp_url
    
    def run(self):
        """Test camera connection"""
        try:
            import cv2
            cap = cv2.VideoCapture(self.rtsp_url)
            
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                
                if ret:
                    self.test_result.emit(True, "تم الاتصال بالكاميرا بنجاح")  # Camera connected successfully
                else:
                    self.test_result.emit(False, "فشل في قراءة الإطار من الكاميرا")  # Failed to read frame
            else:
                self.test_result.emit(False, "فشل في الاتصال بالكاميرا")  # Failed to connect to camera
                
        except Exception as e:
            self.test_result.emit(False, f"خطأ في الاتصال: {str(e)}")  # Connection error

class CameraDialog(QDialog):
    """Camera configuration dialog"""
    
    def __init__(self, camera_data=None, parent=None):
        super().__init__(parent)
        self.camera_data = camera_data
        self.is_edit_mode = camera_data is not None
        self.test_thread = None
        
        self.setup_ui()
        self.setup_style()
        
        if self.is_edit_mode:
            self.load_camera_data()
    
    def setup_ui(self):
        """Setup dialog UI"""
        title = "تعديل الكاميرا" if self.is_edit_mode else "إضافة كاميرا جديدة"  # Edit Camera / Add New Camera
        self.setWindowTitle(title)
        self.setFixedSize(500, 600)
        self.setModal(True)
        
        # Main layout
        main_layout = QVBoxLayout()
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Basic settings tab
        self.setup_basic_tab()
        
        # Advanced settings tab
        self.setup_advanced_tab()
        
        # Test connection tab
        self.setup_test_tab()
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")  # Save
        self.save_button.clicked.connect(self.save_camera)
        
        self.cancel_button = QPushButton("إلغاء")  # Cancel
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.save_button)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        
        # Add to main layout
        main_layout.addWidget(self.tab_widget)
        main_layout.addWidget(self.status_label)
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)
    
    def setup_basic_tab(self):
        """Setup basic settings tab"""
        basic_tab = QWidget()
        layout = QVBoxLayout()
        
        # Camera information group
        info_group = QGroupBox("معلومات الكاميرا")  # Camera Information
        info_layout = QFormLayout()
        
        # Camera name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("اسم الكاميرا")  # Camera name
        info_layout.addRow("الاسم:", self.name_edit)  # Name:
        
        # Location
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("موقع الكاميرا")  # Camera location
        info_layout.addRow("الموقع:", self.location_edit)  # Location:
        
        info_group.setLayout(info_layout)
        
        # Network settings group
        network_group = QGroupBox("إعدادات الشبكة")  # Network Settings
        network_layout = QFormLayout()
        
        # IP Address
        self.ip_edit = QLineEdit()
        self.ip_edit.setPlaceholderText("*************")
        network_layout.addRow("عنوان IP:", self.ip_edit)  # IP Address:
        
        # Port
        self.port_spin = QSpinBox()
        self.port_spin.setRange(1, 65535)
        self.port_spin.setValue(554)
        network_layout.addRow("المنفذ:", self.port_spin)  # Port:
        
        # Username
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("اسم المستخدم")  # Username
        network_layout.addRow("اسم المستخدم:", self.username_edit)  # Username:
        
        # Password
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("كلمة المرور")  # Password
        network_layout.addRow("كلمة المرور:", self.password_edit)  # Password:
        
        network_group.setLayout(network_layout)
        
        # RTSP settings group
        rtsp_group = QGroupBox("إعدادات RTSP")  # RTSP Settings
        rtsp_layout = QFormLayout()
        
        # RTSP URL
        self.rtsp_url_edit = QLineEdit()
        self.rtsp_url_edit.setPlaceholderText("rtsp://*************:554/cam/realmonitor")
        rtsp_layout.addRow("رابط RTSP:", self.rtsp_url_edit)  # RTSP URL:
        
        # Auto-generate button
        self.generate_url_button = QPushButton("توليد تلقائي")  # Auto Generate
        self.generate_url_button.clicked.connect(self.generate_rtsp_url)
        rtsp_layout.addRow("", self.generate_url_button)
        
        rtsp_group.setLayout(rtsp_layout)
        
        # Add to layout
        layout.addWidget(info_group)
        layout.addWidget(network_group)
        layout.addWidget(rtsp_group)
        layout.addStretch()
        
        basic_tab.setLayout(layout)
        self.tab_widget.addTab(basic_tab, "الإعدادات الأساسية")  # Basic Settings
    
    def setup_advanced_tab(self):
        """Setup advanced settings tab"""
        advanced_tab = QWidget()
        layout = QVBoxLayout()
        
        # Features group
        features_group = QGroupBox("الميزات")  # Features
        features_layout = QVBoxLayout()
        
        # Motion detection
        self.motion_detection_check = QCheckBox("تفعيل كشف الحركة")  # Enable motion detection
        self.motion_detection_check.setChecked(True)
        features_layout.addWidget(self.motion_detection_check)
        
        # Recording
        self.recording_enabled_check = QCheckBox("تفعيل التسجيل")  # Enable recording
        self.recording_enabled_check.setChecked(True)
        features_layout.addWidget(self.recording_enabled_check)
        
        features_group.setLayout(features_layout)
        
        # ONVIF settings group
        onvif_group = QGroupBox("إعدادات ONVIF")  # ONVIF Settings
        onvif_layout = QFormLayout()
        
        # ONVIF URL
        self.onvif_url_edit = QLineEdit()
        self.onvif_url_edit.setPlaceholderText("http://*************/onvif/device_service")
        onvif_layout.addRow("رابط ONVIF:", self.onvif_url_edit)  # ONVIF URL:
        
        onvif_group.setLayout(onvif_layout)
        
        # Notes group
        notes_group = QGroupBox("ملاحظات")  # Notes
        notes_layout = QVBoxLayout()
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية حول الكاميرا...")  # Additional notes about camera...
        notes_layout.addWidget(self.notes_edit)
        
        notes_group.setLayout(notes_layout)
        
        # Add to layout
        layout.addWidget(features_group)
        layout.addWidget(onvif_group)
        layout.addWidget(notes_group)
        layout.addStretch()
        
        advanced_tab.setLayout(layout)
        self.tab_widget.addTab(advanced_tab, "الإعدادات المتقدمة")  # Advanced Settings
    
    def setup_test_tab(self):
        """Setup connection test tab"""
        test_tab = QWidget()
        layout = QVBoxLayout()
        
        # Test group
        test_group = QGroupBox("اختبار الاتصال")  # Connection Test
        test_layout = QVBoxLayout()
        
        # Test button
        self.test_button = QPushButton("اختبار الاتصال")  # Test Connection
        self.test_button.clicked.connect(self.test_connection)
        
        # Progress bar
        self.test_progress = QProgressBar()
        self.test_progress.setVisible(False)
        
        # Result label
        self.test_result_label = QLabel("اضغط على 'اختبار الاتصال' للتحقق من الكاميرا")  # Click 'Test Connection' to verify camera
        self.test_result_label.setWordWrap(True)
        
        test_layout.addWidget(self.test_button)
        test_layout.addWidget(self.test_progress)
        test_layout.addWidget(self.test_result_label)
        
        test_group.setLayout(test_layout)
        
        # Discovery group
        discovery_group = QGroupBox("اكتشاف الكاميرات")  # Camera Discovery
        discovery_layout = QVBoxLayout()
        
        self.discover_button = QPushButton("البحث عن الكاميرات في الشبكة")  # Search for cameras on network
        self.discover_button.clicked.connect(self.discover_cameras)
        
        discovery_layout.addWidget(self.discover_button)
        discovery_layout.addWidget(QLabel("سيتم البحث عن الكاميرات في الشبكة المحلية"))  # Will search for cameras on local network
        
        discovery_group.setLayout(discovery_layout)
        
        # Add to layout
        layout.addWidget(test_group)
        layout.addWidget(discovery_group)
        layout.addStretch()
        
        test_tab.setLayout(layout)
        self.tab_widget.addTab(test_tab, "اختبار الاتصال")  # Connection Test
    
    def setup_style(self):
        """Setup dialog styling"""
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QSpinBox, QTextEdit {
                background-color: #404040;
                border: 2px solid #555;
                border-radius: 5px;
                padding: 5px;
                color: white;
            }
            QLineEdit:focus, QSpinBox:focus, QTextEdit:focus {
                border-color: #2196F3;
            }
            QPushButton {
                background-color: #2196F3;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                color: white;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QCheckBox {
                color: white;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #555;
                background-color: #404040;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #2196F3;
                background-color: #2196F3;
                border-radius: 3px;
            }
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #2b2b2b;
            }
            QTabBar::tab {
                background-color: #404040;
                color: white;
                padding: 8px 15px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
            }
        """)
    
    def generate_rtsp_url(self):
        """Generate RTSP URL from IP and credentials"""
        ip = self.ip_edit.text().strip()
        port = self.port_spin.value()
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if not ip:
            self.show_error("يرجى إدخال عنوان IP")  # Please enter IP address
            return
        
        # Build RTSP URL
        if username and password:
            rtsp_url = f"rtsp://{username}:{password}@{ip}:{port}/cam/realmonitor"
        else:
            rtsp_url = f"rtsp://{ip}:{port}/cam/realmonitor"
        
        self.rtsp_url_edit.setText(rtsp_url)
        self.show_success("تم توليد رابط RTSP")  # RTSP URL generated
    
    def test_connection(self):
        """Test camera connection"""
        rtsp_url = self.rtsp_url_edit.text().strip()
        
        if not rtsp_url:
            self.show_error("يرجى إدخال رابط RTSP")  # Please enter RTSP URL
            return
        
        # Start test
        self.test_button.setEnabled(False)
        self.test_progress.setVisible(True)
        self.test_progress.setRange(0, 0)  # Indeterminate progress
        self.test_result_label.setText("جاري اختبار الاتصال...")  # Testing connection...
        
        # Start test thread
        self.test_thread = CameraTestThread(rtsp_url)
        self.test_thread.test_result.connect(self.handle_test_result)
        self.test_thread.start()
    
    def handle_test_result(self, success, message):
        """Handle connection test result"""
        self.test_button.setEnabled(True)
        self.test_progress.setVisible(False)
        
        if success:
            self.test_result_label.setText(f"✓ {message}")
            self.test_result_label.setStyleSheet("color: #4CAF50;")
        else:
            self.test_result_label.setText(f"✗ {message}")
            self.test_result_label.setStyleSheet("color: #f44336;")
    
    def discover_cameras(self):
        """Discover cameras on network"""
        self.show_info("البحث عن الكاميرات", "هذه الميزة قيد التطوير")  # Camera discovery feature under development
    
    def load_camera_data(self):
        """Load existing camera data for editing"""
        if self.camera_data:
            self.name_edit.setText(self.camera_data.get('name', ''))
            self.location_edit.setText(self.camera_data.get('location', ''))
            self.ip_edit.setText(self.camera_data.get('ip_address', ''))
            self.port_spin.setValue(self.camera_data.get('port', 554))
            self.username_edit.setText(self.camera_data.get('username', ''))
            self.password_edit.setText(self.camera_data.get('password', ''))
            self.rtsp_url_edit.setText(self.camera_data.get('rtsp_url', ''))
            self.onvif_url_edit.setText(self.camera_data.get('onvif_url', ''))
            self.motion_detection_check.setChecked(self.camera_data.get('motion_detection', True))
            self.recording_enabled_check.setChecked(self.camera_data.get('recording_enabled', True))
    
    def save_camera(self):
        """Save camera configuration"""
        # Validate input
        if not self.name_edit.text().strip():
            self.show_error("يرجى إدخال اسم الكاميرا")  # Please enter camera name
            return
        
        if not self.ip_edit.text().strip():
            self.show_error("يرجى إدخال عنوان IP")  # Please enter IP address
            return
        
        if not self.rtsp_url_edit.text().strip():
            self.show_error("يرجى إدخال رابط RTSP")  # Please enter RTSP URL
            return
        
        # Collect data
        self.camera_data = {
            'name': self.name_edit.text().strip(),
            'location': self.location_edit.text().strip(),
            'ip_address': self.ip_edit.text().strip(),
            'port': self.port_spin.value(),
            'username': self.username_edit.text().strip(),
            'password': self.password_edit.text().strip(),
            'rtsp_url': self.rtsp_url_edit.text().strip(),
            'onvif_url': self.onvif_url_edit.text().strip(),
            'motion_detection': self.motion_detection_check.isChecked(),
            'recording_enabled': self.recording_enabled_check.isChecked()
        }
        
        self.accept()
    
    def get_camera_data(self):
        """Get camera configuration data"""
        return self.camera_data
    
    def show_error(self, message):
        """Show error message"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: #f44336; margin: 5px;")
    
    def show_success(self, message):
        """Show success message"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: #4CAF50; margin: 5px;")
    
    def show_info(self, title, message):
        """Show info message dialog"""
        QMessageBox.information(self, title, message)
