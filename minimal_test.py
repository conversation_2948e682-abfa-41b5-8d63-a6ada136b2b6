#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SmartPSS Pro - Minimal Test
اختبار أساسي جداً لتشخيص المشكلة
"""

import sys
import os

def test_python():
    """اختبار Python"""
    print(f"Python Version: {sys.version}")
    print(f"Python Path: {sys.executable}")
    return True

def test_pyqt5():
    """اختبار PyQt5"""
    try:
        print("Testing PyQt5 import...")
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
        from PyQt5.QtCore import Qt
        print("✅ PyQt5 imported successfully")
        return True
    except ImportError as e:
        print(f"❌ PyQt5 import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ PyQt5 unexpected error: {e}")
        return False

def create_simple_window():
    """إنشاء نافذة بسيطة جداً"""
    try:
        from PyQt5.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        
        # نافذة بسيطة
        window = QWidget()
        window.setWindowTitle("SmartPSS Test")
        window.setGeometry(300, 300, 300, 200)
        
        # تخطيط بسيط
        layout = QVBoxLayout()
        
        # تسمية بسيطة
        label = QLabel("SmartPSS Pro Test\n\nIf you see this window,\nPyQt5 is working!")
        label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(label)
        window.setLayout(layout)
        
        window.show()
        
        print("✅ Window created successfully")
        print("✅ If you see a window, PyQt5 is working!")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Window creation failed: {e}")
        import traceback
        print(f"Full error: {traceback.format_exc()}")
        return 1

def main():
    """الاختبار الرئيسي"""
    print("=" * 50)
    print("SmartPSS Pro - Minimal Test")
    print("=" * 50)
    
    # اختبار Python
    print("\n1. Testing Python...")
    test_python()
    
    # اختبار PyQt5
    print("\n2. Testing PyQt5...")
    if not test_pyqt5():
        print("\n❌ PyQt5 is not working properly")
        print("Try installing it with: pip install PyQt5")
        input("Press Enter to exit...")
        return 1
    
    # إنشاء نافذة بسيطة
    print("\n3. Creating simple window...")
    return create_simple_window()

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        input("Press Enter to exit...")
        sys.exit(1)
