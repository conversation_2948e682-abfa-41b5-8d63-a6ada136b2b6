#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fixed Main Window UI for SmartPSS Pro
النافذة الرئيسية المصلحة لنظام SmartPSS Pro
"""

import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                           QGridLayout, QPushButton, QLabel, QFrame, QSplitter,
                           QMenuBar, QMenu, QAction, QStatusBar, QToolBar,
                           QScrollArea, QGroupBox, QListWidget, QTabWidget,
                           QMessageBox, QDialog, QApplication, QComboBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPalette, QColor

# Import our modules safely
try:
    from camera_manager import CameraManager
    from motion_detector import MotionManager
    from recorder import RecordingManager
    from user_manager import UserManager
    from settings import SettingsManager
    from alerts import AlertManager
    from database import DatabaseManager
except ImportError as e:
    print(f"تحذير: بعض الوحدات غير متوفرة: {e}")

class MainWindow(QMainWindow):
    """النافذة الرئيسية المصلحة"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize managers safely
        try:
            self.db = DatabaseManager()
            self.user_manager = UserManager()
            self.settings = SettingsManager()
            self.camera_manager = CameraManager()
            self.motion_manager = MotionManager()
            self.recording_manager = RecordingManager()
            self.alert_manager = AlertManager()
        except Exception as e:
            print(f"تحذير: خطأ في تهيئة المدراء: {e}")
        
        # UI components
        self.camera_widgets = {}
        self.current_user = None
        
        # Setup UI
        self.setup_ui()
        self.setup_connections()
        self.apply_theme()
        
        # Start managers safely
        try:
            if hasattr(self, 'alert_manager'):
                self.alert_manager.start()
        except Exception as e:
            print(f"تحذير: خطأ في بدء المدراء: {e}")
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(1000)  # Update every second

        # تحميل الكاميرات الموجودة
        self.load_existing_cameras()

    def setup_ui(self):
        """Setup main window UI"""
        self.setWindowTitle("SmartPSS Pro - نظام المراقبة الذكي")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout()
        
        # Create splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel (cameras grid)
        self.setup_camera_panel(splitter)
        
        # Right panel (controls and info)
        self.setup_control_panel(splitter)
        
        # Set splitter proportions
        splitter.setSizes([800, 400])
        
        main_layout.addWidget(splitter)
        central_widget.setLayout(main_layout)
        
        # Setup menu bar
        self.setup_menu_bar()
        
        # Setup toolbar
        self.setup_toolbar()
        
        # Setup status bar
        self.setup_status_bar()

    def setup_camera_panel(self, parent):
        """Setup camera display panel"""
        camera_widget = QWidget()
        camera_layout = QVBoxLayout()

        # شريط أدوات الكاميرات
        camera_toolbar = QHBoxLayout()

        # أزرار التحكم في العرض
        self.grid_combo = QComboBox()
        self.grid_combo.addItems(["1x1", "2x2", "3x3", "4x4", "2x3", "3x4"])
        self.grid_combo.setCurrentText("2x2")
        self.grid_combo.currentTextChanged.connect(self.change_camera_grid)

        # أزرار التكبير والتصغير
        zoom_in_btn = QPushButton("🔍+ تكبير")
        zoom_in_btn.clicked.connect(self.zoom_in_cameras)
        zoom_in_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)

        zoom_out_btn = QPushButton("🔍- تصغير")
        zoom_out_btn.clicked.connect(self.zoom_out_cameras)
        zoom_out_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)

        reset_zoom_btn = QPushButton("↻ إعادة تعيين")
        reset_zoom_btn.clicked.connect(self.reset_camera_zoom)
        reset_zoom_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
        """)

        fullscreen_btn = QPushButton("🔳 ملء الشاشة")
        fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        fullscreen_btn.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7B1FA2;
            }
        """)

        snapshot_all_btn = QPushButton("📷 لقطة للكل")
        snapshot_all_btn.clicked.connect(self.take_all_snapshots)
        snapshot_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #455A64;
            }
        """)

        record_all_btn = QPushButton("🔴 تسجيل الكل")
        record_all_btn.clicked.connect(self.toggle_all_recording)
        record_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)

        # إضافة العناصر لشريط الأدوات
        camera_toolbar.addWidget(QLabel("تخطيط الشبكة:"))
        camera_toolbar.addWidget(self.grid_combo)
        camera_toolbar.addWidget(QLabel("|"))
        camera_toolbar.addWidget(zoom_in_btn)
        camera_toolbar.addWidget(zoom_out_btn)
        camera_toolbar.addWidget(reset_zoom_btn)
        camera_toolbar.addStretch()
        camera_toolbar.addWidget(snapshot_all_btn)
        camera_toolbar.addWidget(record_all_btn)
        camera_toolbar.addWidget(fullscreen_btn)

        camera_layout.addLayout(camera_toolbar)

        # Camera grid
        self.camera_scroll = QScrollArea()
        self.camera_grid_widget = QWidget()
        self.camera_grid_layout = QGridLayout()

        # متغيرات التكبير
        self.camera_zoom_factor = 1.0
        self.base_camera_size = (320, 240)

        self.camera_grid_widget.setLayout(self.camera_grid_layout)
        self.camera_scroll.setWidget(self.camera_grid_widget)
        self.camera_scroll.setWidgetResizable(True)

        camera_layout.addWidget(self.camera_scroll)
        camera_widget.setLayout(camera_layout)

        parent.addWidget(camera_widget)
    
    def setup_control_panel(self, parent):
        """Setup control panel"""
        control_widget = QWidget()
        control_layout = QVBoxLayout()
        
        # Tab widget for different controls
        self.control_tabs = QTabWidget()
        
        # Camera management tab
        self.setup_camera_management_tab()
        
        # Recording tab
        self.setup_recording_tab()
        
        # Alerts tab
        self.setup_alerts_tab()
        
        # System info tab
        self.setup_system_info_tab()
        
        control_layout.addWidget(self.control_tabs)
        control_widget.setLayout(control_layout)
        
        parent.addWidget(control_widget)
    
    def setup_camera_management_tab(self):
        """Setup camera management tab"""
        camera_tab = QWidget()
        layout = QVBoxLayout()
        
        # Camera list
        self.camera_list = QListWidget()
        layout.addWidget(QLabel("الكاميرات:"))
        layout.addWidget(self.camera_list)
        
        # Buttons
        button_layout = QHBoxLayout()

        self.add_camera_btn = QPushButton("إضافة كاميرا")
        self.add_camera_btn.clicked.connect(self.show_add_camera_dialog)

        self.edit_camera_btn = QPushButton("تعديل")
        self.edit_camera_btn.clicked.connect(self.edit_selected_camera)

        self.delete_camera_btn = QPushButton("حذف")
        self.delete_camera_btn.clicked.connect(self.delete_selected_camera)

        button_layout.addWidget(self.add_camera_btn)
        button_layout.addWidget(self.edit_camera_btn)
        button_layout.addWidget(self.delete_camera_btn)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        camera_tab.setLayout(layout)
        self.control_tabs.addTab(camera_tab, "الكاميرات")
    
    def setup_recording_tab(self):
        """Setup recording control tab"""
        recording_tab = QWidget()
        layout = QVBoxLayout()
        
        # Recording controls
        record_group = QGroupBox("التحكم في التسجيل")
        record_layout = QVBoxLayout()
        
        self.record_all_button = QPushButton("تسجيل جميع الكاميرات")
        self.stop_all_button = QPushButton("إيقاف جميع التسجيلات")
        
        record_layout.addWidget(self.record_all_button)
        record_layout.addWidget(self.stop_all_button)
        record_group.setLayout(record_layout)
        
        # Recording list
        recordings_group = QGroupBox("التسجيلات الحديثة")
        recordings_layout = QVBoxLayout()
        
        self.recordings_list = QListWidget()
        recordings_layout.addWidget(self.recordings_list)
        
        recordings_group.setLayout(recordings_layout)
        
        layout.addWidget(record_group)
        layout.addWidget(recordings_group)
        
        recording_tab.setLayout(layout)
        self.control_tabs.addTab(recording_tab, "التسجيل")
    
    def setup_alerts_tab(self):
        """Setup alerts tab"""
        alerts_tab = QWidget()
        layout = QVBoxLayout()
        
        # Alert list
        self.alerts_list = QListWidget()
        layout.addWidget(QLabel("التنبيهات الحديثة:"))
        layout.addWidget(self.alerts_list)
        
        # Clear button
        clear_button = QPushButton("مسح التنبيهات")
        layout.addWidget(clear_button)
        
        alerts_tab.setLayout(layout)
        self.control_tabs.addTab(alerts_tab, "التنبيهات")
    
    def setup_system_info_tab(self):
        """Setup system information tab"""
        info_tab = QWidget()
        layout = QVBoxLayout()
        
        # System status
        self.system_status_label = QLabel("حالة النظام: جاهز")
        self.user_info_label = QLabel("المستخدم: غير مسجل")
        self.cameras_count_label = QLabel("عدد الكاميرات: 0")
        self.recordings_count_label = QLabel("التسجيلات النشطة: 0")
        
        layout.addWidget(self.system_status_label)
        layout.addWidget(self.user_info_label)
        layout.addWidget(self.cameras_count_label)
        layout.addWidget(self.recordings_count_label)
        layout.addStretch()
        
        info_tab.setLayout(layout)
        self.control_tabs.addTab(info_tab, "معلومات النظام")
    
    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('ملف')
        
        login_action = QAction('تسجيل الدخول', self)
        login_action.triggered.connect(self.show_login_dialog)
        file_menu.addAction(login_action)
        
        logout_action = QAction('تسجيل الخروج', self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu('عرض')
        
        # Tools menu
        tools_menu = menubar.addMenu('أدوات')
        
        settings_action = QAction('الإعدادات', self)
        settings_action.triggered.connect(self.show_settings_dialog)
        tools_menu.addAction(settings_action)
        
        # Help menu
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """Setup toolbar"""
        toolbar = self.addToolBar('الأدوات الرئيسية')
        
        # Add camera action
        add_camera_action = QAction('إضافة كاميرا', self)
        add_camera_action.triggered.connect(self.show_add_camera_dialog)
        toolbar.addAction(add_camera_action)
        
        toolbar.addSeparator()
        
        # Record all action
        record_all_action = QAction('تسجيل الكل', self)
        record_all_action.triggered.connect(self.record_all_cameras)
        toolbar.addAction(record_all_action)
        
        # Stop all action
        stop_all_action = QAction('إيقاف الكل', self)
        stop_all_action.triggered.connect(self.stop_all_recordings)
        toolbar.addAction(stop_all_action)
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage('جاهز')
    
    def setup_connections(self):
        """Setup signal connections"""
        try:
            if hasattr(self, 'alert_manager'):
                self.alert_manager.alert_triggered.connect(self.handle_alert)
            
            if hasattr(self, 'motion_manager'):
                self.motion_manager.add_motion_callback(self.handle_motion_detected)
        except Exception as e:
            print(f"تحذير: خطأ في ربط الإشارات: {e}")
    
    def apply_theme(self):
        """Apply dark theme"""
        try:
            if hasattr(self, 'settings'):
                theme_settings = self.settings.apply_theme_settings()
                self.setStyleSheet(theme_settings['style_sheet'])
                self.setWindowOpacity(theme_settings['opacity'])
        except Exception as e:
            print(f"تحذير: خطأ في تطبيق المظهر: {e}")
    
    def show_login_dialog(self):
        """Show login dialog"""
        try:
            from ui.login_dialog import LoginDialog
            dialog = LoginDialog(self.user_manager, self)
            if dialog.exec_() == QDialog.Accepted:
                self.current_user = self.user_manager.get_current_user()
                self.update_user_interface()
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
    
    def logout(self):
        """Logout current user"""
        try:
            if hasattr(self, 'user_manager'):
                self.user_manager.logout()
            self.current_user = None
            self.update_user_interface()
        except Exception as e:
            print(f"خطأ في تسجيل الخروج: {e}")
    
    def update_user_interface(self):
        """Update UI based on user permissions"""
        try:
            if self.current_user:
                self.user_info_label.setText(f"المستخدم: {self.current_user['username']}")
            else:
                self.user_info_label.setText("المستخدم: غير مسجل")
        except Exception as e:
            print(f"خطأ في تحديث الواجهة: {e}")
    
    def update_status(self):
        """Update status information"""
        try:
            if hasattr(self, 'camera_manager'):
                cameras = self.camera_manager.get_cameras()
                self.cameras_count_label.setText(f"عدد الكاميرات: {len(cameras)}")
            
            if hasattr(self, 'recording_manager'):
                active_recordings = len(self.recording_manager.active_recordings)
                self.recordings_count_label.setText(f"التسجيلات النشطة: {active_recordings}")
        except Exception as e:
            pass  # Silent fail for status updates
    
    def handle_alert(self, alert_type, message, data):
        """Handle alert from alert manager"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            alert_text = f"[{timestamp}] {message}"
            self.alerts_list.addItem(alert_text)
            self.alerts_list.scrollToBottom()
            self.status_bar.showMessage(message, 5000)
        except Exception as e:
            print(f"خطأ في معالجة التنبيه: {e}")
    
    def handle_motion_detected(self, camera_id, confidence, frame, snapshot_path):
        """Handle motion detection"""
        try:
            if camera_id in self.camera_widgets:
                self.camera_widgets[camera_id].set_motion_status(True)
            
            camera_name = f"Camera {camera_id}"
            if hasattr(self, 'alert_manager'):
                self.alert_manager.motion_detected(camera_id, camera_name, confidence, snapshot_path)
        except Exception as e:
            print(f"خطأ في معالجة كشف الحركة: {e}")
    
    def show_add_camera_dialog(self):
        """Show add camera dialog"""
        try:
            from ui.add_camera_dialog import AddCameraDialog
            dialog = AddCameraDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                camera_data = dialog.get_camera_data()
                if camera_data:
                    self.add_camera_to_system(camera_data)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إضافة الكاميرا:\n{str(e)}")

    def show_settings_dialog(self):
        """Show settings dialog"""
        try:
            from ui.settings_dialog import SettingsDialog
            dialog = SettingsDialog(self.settings, self)
            dialog.settings_changed.connect(self.on_settings_changed)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الإعدادات:\n{str(e)}")

    def add_camera_to_system(self, camera_data):
        """إضافة كاميرا جديدة للنظام"""
        try:
            # تحويل البيانات لتتوافق مع قاعدة البيانات
            db_camera_data = self.convert_camera_data_for_db(camera_data)

            # إضافة الكاميرا لمدير الكاميرات
            if hasattr(self, 'camera_manager'):
                camera_id = self.camera_manager.add_camera(db_camera_data)

                # إضافة الكاميرا للقائمة
                self.camera_list.addItem(f"{camera_data['name']} - {camera_data['type']}")

                # إنشاء ويدجت الكاميرا وإضافته للشبكة
                self.add_camera_widget(camera_id, camera_data['name'], db_camera_data.get('rtsp_url', ''))

                # تحديث عدد الكاميرات
                self.update_status()

                QMessageBox.information(self, "تم الإضافة", f"تم إضافة الكاميرا '{camera_data['name']}' بنجاح!")
            else:
                QMessageBox.warning(self, "تحذير", "مدير الكاميرات غير متوفر")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الكاميرا:\n{str(e)}")

    def add_camera_widget(self, camera_id, camera_name, rtsp_url):
        """إضافة ويدجت كاميرا جديد للشبكة"""
        try:
            from ui.camera_widget import CameraWidget

            # إنشاء ويدجت الكاميرا
            camera_widget = CameraWidget(camera_id, camera_name)

            # ربط الإشارات
            camera_widget.camera_clicked.connect(self.on_camera_clicked)
            camera_widget.start_recording.connect(self.on_start_recording)
            camera_widget.stop_recording.connect(self.on_stop_recording)
            camera_widget.take_snapshot.connect(self.on_take_snapshot)

            # حفظ مرجع الويدجت
            self.camera_widgets[camera_id] = camera_widget

            # إضافة للشبكة
            self.add_widget_to_grid(camera_widget)

            # بدء البث إذا كان URL متوفر
            if rtsp_url and hasattr(self, 'camera_manager'):
                self.start_camera_stream(camera_id, rtsp_url, camera_widget)

        except Exception as e:
            print(f"خطأ في إضافة ويدجت الكاميرا: {e}")

    def add_widget_to_grid(self, widget):
        """إضافة ويدجت للشبكة"""
        try:
            # حساب الموقع في الشبكة (2x2 افتراضياً)
            current_count = len(self.camera_widgets)
            row = (current_count - 1) // 2
            col = (current_count - 1) % 2

            # إضافة للشبكة
            self.camera_grid_layout.addWidget(widget, row, col)

        except Exception as e:
            print(f"خطأ في إضافة الويدجت للشبكة: {e}")

    def start_camera_stream(self, camera_id, rtsp_url, camera_widget):
        """بدء بث الكاميرا"""
        try:
            if hasattr(self, 'camera_manager'):
                # بدء البث
                stream = self.camera_manager.start_stream(camera_id, rtsp_url)

                if stream:
                    # ربط إشارة الإطارات الجديدة
                    stream.frame_ready.connect(lambda frame, cid: self.on_frame_ready(frame, cid))
                    stream.connection_lost.connect(lambda cid: self.on_connection_lost(cid))
                    stream.connection_restored.connect(lambda cid: self.on_connection_restored(cid))

                    print(f"تم بدء بث الكاميرا {camera_id}")
                else:
                    print(f"فشل في بدء بث الكاميرا {camera_id}")

        except Exception as e:
            print(f"خطأ في بدء بث الكاميرا: {e}")

    def on_frame_ready(self, frame, camera_id):
        """معالجة الإطار الجديد"""
        try:
            if camera_id in self.camera_widgets:
                self.camera_widgets[camera_id].update_frame(frame)
        except Exception as e:
            print(f"خطأ في معالجة الإطار: {e}")

    def on_connection_lost(self, camera_id):
        """معالجة فقدان الاتصال"""
        try:
            if camera_id in self.camera_widgets:
                self.camera_widgets[camera_id].set_connection_status(False)
        except Exception as e:
            print(f"خطأ في معالجة فقدان الاتصال: {e}")

    def on_connection_restored(self, camera_id):
        """معالجة استعادة الاتصال"""
        try:
            if camera_id in self.camera_widgets:
                self.camera_widgets[camera_id].set_connection_status(True)
        except Exception as e:
            print(f"خطأ في معالجة استعادة الاتصال: {e}")

    def on_camera_clicked(self, camera_id):
        """معالجة النقر على الكاميرا"""
        print(f"تم النقر على الكاميرا {camera_id}")
        # يمكن إضافة ميزة ملء الشاشة هنا

    def on_start_recording(self, camera_id):
        """بدء تسجيل الكاميرا"""
        try:
            if hasattr(self, 'recording_manager'):
                self.recording_manager.start_manual_recording(camera_id)
                print(f"تم بدء تسجيل الكاميرا {camera_id}")
        except Exception as e:
            print(f"خطأ في بدء التسجيل: {e}")

    def on_stop_recording(self, camera_id):
        """إيقاف تسجيل الكاميرا"""
        try:
            if hasattr(self, 'recording_manager'):
                self.recording_manager.stop_recording(camera_id)
                print(f"تم إيقاف تسجيل الكاميرا {camera_id}")
        except Exception as e:
            print(f"خطأ في إيقاف التسجيل: {e}")

    def on_take_snapshot(self, camera_id):
        """أخذ لقطة من الكاميرا"""
        try:
            if camera_id in self.camera_widgets:
                widget = self.camera_widgets[camera_id]
                if widget.last_frame is not None:
                    # حفظ اللقطة
                    import cv2
                    from datetime import datetime

                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"snapshots/camera_{camera_id}_{timestamp}.jpg"

                    cv2.imwrite(filename, widget.last_frame)
                    print(f"تم حفظ اللقطة: {filename}")

                    QMessageBox.information(self, "تم الحفظ", f"تم حفظ اللقطة في:\n{filename}")
                else:
                    QMessageBox.warning(self, "تحذير", "لا يوجد إطار متاح للحفظ")
        except Exception as e:
            print(f"خطأ في أخذ اللقطة: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في أخذ اللقطة:\n{str(e)}")

    def load_existing_cameras(self):
        """تحميل الكاميرات الموجودة من قاعدة البيانات"""
        try:
            if hasattr(self, 'camera_manager'):
                cameras = self.camera_manager.get_cameras()

                for camera in cameras:
                    # استخراج البيانات من قاعدة البيانات
                    camera_id = camera[0]
                    camera_name = camera[1]
                    rtsp_url = camera[6]  # rtsp_url في العمود السادس

                    # إضافة للقائمة
                    self.camera_list.addItem(f"{camera_name} - متصل")

                    # إنشاء ويدجت الكاميرا
                    self.add_camera_widget(camera_id, camera_name, rtsp_url)

                print(f"تم تحميل {len(cameras)} كاميرا")

        except Exception as e:
            print(f"خطأ في تحميل الكاميرات: {e}")

    def edit_selected_camera(self):
        """تعديل الكاميرا المحددة"""
        try:
            current_item = self.camera_list.currentItem()
            if current_item:
                QMessageBox.information(self, "تعديل الكاميرا", "ميزة تعديل الكاميرا ستكون متاحة قريباً!")
            else:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار كاميرا للتعديل")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تعديل الكاميرا:\n{str(e)}")

    def delete_selected_camera(self):
        """حذف الكاميرا المحددة"""
        try:
            current_item = self.camera_list.currentItem()
            if current_item:
                reply = QMessageBox.question(
                    self, "تأكيد الحذف",
                    "هل أنت متأكد من حذف هذه الكاميرا؟",
                    QMessageBox.Yes | QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    # حذف من القائمة
                    row = self.camera_list.row(current_item)
                    self.camera_list.takeItem(row)

                    QMessageBox.information(self, "تم الحذف", "تم حذف الكاميرا بنجاح!")

                    # تحديث العدد
                    self.update_status()
            else:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار كاميرا للحذف")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في حذف الكاميرا:\n{str(e)}")

    def change_camera_grid(self, grid_layout):
        """تغيير تخطيط شبكة الكاميرات"""
        try:
            print(f"تغيير تخطيط الشبكة إلى: {grid_layout}")

            # استخراج الأبعاد
            if 'x' in grid_layout:
                cols, rows = map(int, grid_layout.split('x'))
            else:
                cols, rows = 2, 2  # افتراضي

            # إعادة ترتيب الكاميرات
            self.rearrange_cameras(rows, cols)

        except Exception as e:
            print(f"خطأ في تغيير تخطيط الشبكة: {e}")

    def rearrange_cameras(self, rows, cols):
        """إعادة ترتيب الكاميرات في الشبكة"""
        try:
            # إزالة جميع الويدجت من الشبكة
            for i in reversed(range(self.camera_grid_layout.count())):
                widget = self.camera_grid_layout.itemAt(i).widget()
                if widget:
                    self.camera_grid_layout.removeWidget(widget)

            # إعادة إضافة الكاميرات بالترتيب الجديد
            camera_count = 0
            for camera_id, widget in self.camera_widgets.items():
                row = camera_count // cols
                col = camera_count % cols

                if row < rows:  # فقط إذا كان هناك مساحة
                    self.camera_grid_layout.addWidget(widget, row, col)
                    widget.show()
                    camera_count += 1
                else:
                    widget.hide()  # إخفاء الكاميرات الزائدة

            print(f"تم إعادة ترتيب {camera_count} كاميرا في شبكة {rows}x{cols}")

        except Exception as e:
            print(f"خطأ في إعادة ترتيب الكاميرات: {e}")

    def zoom_in_cameras(self):
        """تكبير الكاميرات"""
        try:
            if hasattr(self, 'camera_zoom_factor'):
                self.camera_zoom_factor = min(self.camera_zoom_factor * 1.2, 3.0)
            else:
                self.camera_zoom_factor = 1.2

            self.apply_camera_zoom()
            print(f"تكبير الكاميرات: {self.camera_zoom_factor:.1f}x")

        except Exception as e:
            print(f"خطأ في تكبير الكاميرات: {e}")

    def zoom_out_cameras(self):
        """تصغير الكاميرات"""
        try:
            if hasattr(self, 'camera_zoom_factor'):
                self.camera_zoom_factor = max(self.camera_zoom_factor / 1.2, 0.5)
            else:
                self.camera_zoom_factor = 0.8

            self.apply_camera_zoom()
            print(f"تصغير الكاميرات: {self.camera_zoom_factor:.1f}x")

        except Exception as e:
            print(f"خطأ في تصغير الكاميرات: {e}")

    def reset_camera_zoom(self):
        """إعادة تعيين تكبير الكاميرات"""
        try:
            self.camera_zoom_factor = 1.0
            self.apply_camera_zoom()
            print("تم إعادة تعيين تكبير الكاميرات")

        except Exception as e:
            print(f"خطأ في إعادة تعيين التكبير: {e}")

    def apply_camera_zoom(self):
        """تطبيق التكبير على جميع الكاميرات"""
        try:
            if not hasattr(self, 'base_camera_size'):
                self.base_camera_size = (320, 240)

            # حساب الحجم الجديد
            new_width = int(self.base_camera_size[0] * self.camera_zoom_factor)
            new_height = int(self.base_camera_size[1] * self.camera_zoom_factor)

            # تطبيق على جميع الكاميرات
            for widget in self.camera_widgets.values():
                widget.setFixedSize(new_width, new_height)

            # تحديث منطقة التمرير
            if hasattr(self, 'camera_scroll'):
                self.camera_scroll.updateGeometry()

        except Exception as e:
            print(f"خطأ في تطبيق التكبير: {e}")

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        try:
            if self.isFullScreen():
                self.showNormal()
                print("خروج من وضع ملء الشاشة")
            else:
                self.showFullScreen()
                print("دخول وضع ملء الشاشة")
        except Exception as e:
            print(f"خطأ في تبديل ملء الشاشة: {e}")

    def take_all_snapshots(self):
        """أخذ لقطات لجميع الكاميرات"""
        try:
            snapshot_count = 0
            for camera_id in self.camera_widgets.keys():
                self.on_take_snapshot(camera_id)
                snapshot_count += 1

            if snapshot_count > 0:
                QMessageBox.information(self, "تم الحفظ", f"تم أخذ {snapshot_count} لقطة وحفظها في مجلد snapshots/")
            else:
                QMessageBox.warning(self, "تحذير", "لا توجد كاميرات متاحة")

        except Exception as e:
            print(f"خطأ في أخذ لقطات جميع الكاميرات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في أخذ اللقطات:\n{str(e)}")

    def toggle_all_recording(self):
        """تبديل تسجيل جميع الكاميرات"""
        try:
            # فحص حالة التسجيل الحالية
            recording_cameras = 0
            for widget in self.camera_widgets.values():
                if widget.is_recording:
                    recording_cameras += 1

            if recording_cameras > 0:
                # إيقاف جميع التسجيلات
                for camera_id in self.camera_widgets.keys():
                    self.on_stop_recording(camera_id)
                print("تم إيقاف تسجيل جميع الكاميرات")
                QMessageBox.information(self, "تم الإيقاف", "تم إيقاف تسجيل جميع الكاميرات")
            else:
                # بدء تسجيل جميع الكاميرات
                for camera_id in self.camera_widgets.keys():
                    self.on_start_recording(camera_id)
                print("تم بدء تسجيل جميع الكاميرات")
                QMessageBox.information(self, "تم البدء", "تم بدء تسجيل جميع الكاميرات")

        except Exception as e:
            print(f"خطأ في تبديل تسجيل جميع الكاميرات: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تبديل التسجيل:\n{str(e)}")

    def convert_camera_data_for_db(self, camera_data):
        """تحويل بيانات الكاميرا لتتوافق مع قاعدة البيانات"""
        try:
            # استخراج IP والمنفذ من URL
            url = camera_data.get('url', '')
            ip_address = ''
            port = 554  # المنفذ الافتراضي لـ RTSP

            if camera_data['type'] == 'USB Camera':
                ip_address = 'localhost'
                port = 0
            elif '://' in url:
                # استخراج IP والمنفذ من URL
                parts = url.split('://')
                if len(parts) > 1:
                    address_part = parts[1].split('/')[0]
                    if '@' in address_part:
                        address_part = address_part.split('@')[1]

                    if ':' in address_part:
                        ip_address, port_str = address_part.split(':')
                        try:
                            port = int(port_str)
                        except:
                            port = 554
                    else:
                        ip_address = address_part
            else:
                # إذا كان URL مجرد IP
                if ':' in url:
                    ip_address, port_str = url.split(':')
                    try:
                        port = int(port_str)
                    except:
                        port = 554
                else:
                    ip_address = url

            # بناء البيانات المتوافقة مع قاعدة البيانات
            db_data = {
                'name': camera_data.get('name', ''),
                'ip_address': ip_address,
                'port': port,
                'username': camera_data.get('username', ''),
                'password': camera_data.get('password', ''),
                'rtsp_url': url,
                'location': camera_data.get('description', ''),
                'motion_detection': camera_data.get('motion_detect', True),
                'recording_enabled': camera_data.get('auto_record', True)
            }

            return db_data

        except Exception as e:
            print(f"خطأ في تحويل بيانات الكاميرا: {e}")
            # إرجاع بيانات افتراضية في حالة الخطأ
            return {
                'name': camera_data.get('name', 'Unknown Camera'),
                'ip_address': 'localhost',
                'port': 554,
                'username': camera_data.get('username', ''),
                'password': camera_data.get('password', ''),
                'rtsp_url': camera_data.get('url', ''),
                'location': camera_data.get('description', ''),
                'motion_detection': True,
                'recording_enabled': True
            }

    def on_settings_changed(self):
        """معالجة تغيير الإعدادات"""
        try:
            # إعادة تطبيق المظهر
            self.apply_theme()

            # تحديث الواجهة حسب الإعدادات الجديدة
            self.update_user_interface()

        except Exception as e:
            print(f"خطأ في تطبيق الإعدادات الجديدة: {e}")
    
    def show_about_dialog(self):
        """Show about dialog"""
        QMessageBox.about(self, "حول البرنامج", 
                         "SmartPSS Pro - نظام المراقبة الذكي\nإصدار 2.0\n\nنظام مراقبة متقدم مع كشف الحركة والتسجيل التلقائي")
    
    def record_all_cameras(self):
        """Start recording all cameras"""
        try:
            if hasattr(self, 'recording_manager'):
                for camera_id in self.camera_widgets.keys():
                    self.recording_manager.start_manual_recording(camera_id)
        except Exception as e:
            print(f"خطأ في بدء التسجيل: {e}")
    
    def stop_all_recordings(self):
        """Stop all active recordings"""
        try:
            if hasattr(self, 'recording_manager'):
                self.recording_manager.stop_all_recordings()
        except Exception as e:
            print(f"خطأ في إيقاف التسجيل: {e}")
    
    def closeEvent(self, event):
        """Handle window close event"""
        try:
            if hasattr(self, 'camera_manager'):
                self.camera_manager.stop_all_streams()
            if hasattr(self, 'motion_manager'):
                self.motion_manager.stop_all_detection()
            if hasattr(self, 'recording_manager'):
                self.recording_manager.cleanup()
            if hasattr(self, 'alert_manager'):
                self.alert_manager.stop()
        except Exception as e:
            print(f"خطأ في إغلاق النافذة: {e}")
        
        event.accept()
