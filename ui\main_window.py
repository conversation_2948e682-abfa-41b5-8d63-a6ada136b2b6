#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fixed Main Window UI for SmartPSS Pro
النافذة الرئيسية المصلحة لنظام SmartPSS Pro
"""

import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                           QGridLayout, QPushButton, QLabel, QFrame, QSplitter,
                           QMenuBar, QMenu, QAction, QStatusBar, QToolBar,
                           QScrollArea, QGroupBox, QListWidget, QTabWidget,
                           QMessageBox, QDialog, QApplication)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPalette, QColor

# Import our modules safely
try:
    from camera_manager import CameraManager
    from motion_detector import MotionManager
    from recorder import RecordingManager
    from user_manager import UserManager
    from settings import SettingsManager
    from alerts import AlertManager
    from database import DatabaseManager
except ImportError as e:
    print(f"تحذير: بعض الوحدات غير متوفرة: {e}")

class MainWindow(QMainWindow):
    """النافذة الرئيسية المصلحة"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize managers safely
        try:
            self.db = DatabaseManager()
            self.user_manager = UserManager()
            self.settings = SettingsManager()
            self.camera_manager = CameraManager()
            self.motion_manager = MotionManager()
            self.recording_manager = RecordingManager()
            self.alert_manager = AlertManager()
        except Exception as e:
            print(f"تحذير: خطأ في تهيئة المدراء: {e}")
        
        # UI components
        self.camera_widgets = {}
        self.current_user = None
        
        # Setup UI
        self.setup_ui()
        self.setup_connections()
        self.apply_theme()
        
        # Start managers safely
        try:
            if hasattr(self, 'alert_manager'):
                self.alert_manager.start()
        except Exception as e:
            print(f"تحذير: خطأ في بدء المدراء: {e}")
        
        # Update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(1000)  # Update every second

    def setup_ui(self):
        """Setup main window UI"""
        self.setWindowTitle("SmartPSS Pro - نظام المراقبة الذكي")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout()
        
        # Create splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel (cameras grid)
        self.setup_camera_panel(splitter)
        
        # Right panel (controls and info)
        self.setup_control_panel(splitter)
        
        # Set splitter proportions
        splitter.setSizes([800, 400])
        
        main_layout.addWidget(splitter)
        central_widget.setLayout(main_layout)
        
        # Setup menu bar
        self.setup_menu_bar()
        
        # Setup toolbar
        self.setup_toolbar()
        
        # Setup status bar
        self.setup_status_bar()

    def setup_camera_panel(self, parent):
        """Setup camera display panel"""
        camera_widget = QWidget()
        camera_layout = QVBoxLayout()
        
        # Camera grid
        self.camera_scroll = QScrollArea()
        self.camera_grid_widget = QWidget()
        self.camera_grid_layout = QGridLayout()
        
        self.camera_grid_widget.setLayout(self.camera_grid_layout)
        self.camera_scroll.setWidget(self.camera_grid_widget)
        self.camera_scroll.setWidgetResizable(True)
        
        camera_layout.addWidget(self.camera_scroll)
        camera_widget.setLayout(camera_layout)
        
        parent.addWidget(camera_widget)
    
    def setup_control_panel(self, parent):
        """Setup control panel"""
        control_widget = QWidget()
        control_layout = QVBoxLayout()
        
        # Tab widget for different controls
        self.control_tabs = QTabWidget()
        
        # Camera management tab
        self.setup_camera_management_tab()
        
        # Recording tab
        self.setup_recording_tab()
        
        # Alerts tab
        self.setup_alerts_tab()
        
        # System info tab
        self.setup_system_info_tab()
        
        control_layout.addWidget(self.control_tabs)
        control_widget.setLayout(control_layout)
        
        parent.addWidget(control_widget)
    
    def setup_camera_management_tab(self):
        """Setup camera management tab"""
        camera_tab = QWidget()
        layout = QVBoxLayout()
        
        # Camera list
        self.camera_list = QListWidget()
        layout.addWidget(QLabel("الكاميرات:"))
        layout.addWidget(self.camera_list)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        add_button = QPushButton("إضافة كاميرا")
        edit_button = QPushButton("تعديل")
        delete_button = QPushButton("حذف")
        
        button_layout.addWidget(add_button)
        button_layout.addWidget(edit_button)
        button_layout.addWidget(delete_button)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        camera_tab.setLayout(layout)
        self.control_tabs.addTab(camera_tab, "الكاميرات")
    
    def setup_recording_tab(self):
        """Setup recording control tab"""
        recording_tab = QWidget()
        layout = QVBoxLayout()
        
        # Recording controls
        record_group = QGroupBox("التحكم في التسجيل")
        record_layout = QVBoxLayout()
        
        self.record_all_button = QPushButton("تسجيل جميع الكاميرات")
        self.stop_all_button = QPushButton("إيقاف جميع التسجيلات")
        
        record_layout.addWidget(self.record_all_button)
        record_layout.addWidget(self.stop_all_button)
        record_group.setLayout(record_layout)
        
        # Recording list
        recordings_group = QGroupBox("التسجيلات الحديثة")
        recordings_layout = QVBoxLayout()
        
        self.recordings_list = QListWidget()
        recordings_layout.addWidget(self.recordings_list)
        
        recordings_group.setLayout(recordings_layout)
        
        layout.addWidget(record_group)
        layout.addWidget(recordings_group)
        
        recording_tab.setLayout(layout)
        self.control_tabs.addTab(recording_tab, "التسجيل")
    
    def setup_alerts_tab(self):
        """Setup alerts tab"""
        alerts_tab = QWidget()
        layout = QVBoxLayout()
        
        # Alert list
        self.alerts_list = QListWidget()
        layout.addWidget(QLabel("التنبيهات الحديثة:"))
        layout.addWidget(self.alerts_list)
        
        # Clear button
        clear_button = QPushButton("مسح التنبيهات")
        layout.addWidget(clear_button)
        
        alerts_tab.setLayout(layout)
        self.control_tabs.addTab(alerts_tab, "التنبيهات")
    
    def setup_system_info_tab(self):
        """Setup system information tab"""
        info_tab = QWidget()
        layout = QVBoxLayout()
        
        # System status
        self.system_status_label = QLabel("حالة النظام: جاهز")
        self.user_info_label = QLabel("المستخدم: غير مسجل")
        self.cameras_count_label = QLabel("عدد الكاميرات: 0")
        self.recordings_count_label = QLabel("التسجيلات النشطة: 0")
        
        layout.addWidget(self.system_status_label)
        layout.addWidget(self.user_info_label)
        layout.addWidget(self.cameras_count_label)
        layout.addWidget(self.recordings_count_label)
        layout.addStretch()
        
        info_tab.setLayout(layout)
        self.control_tabs.addTab(info_tab, "معلومات النظام")
    
    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('ملف')
        
        login_action = QAction('تسجيل الدخول', self)
        login_action.triggered.connect(self.show_login_dialog)
        file_menu.addAction(login_action)
        
        logout_action = QAction('تسجيل الخروج', self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu('عرض')
        
        # Tools menu
        tools_menu = menubar.addMenu('أدوات')
        
        settings_action = QAction('الإعدادات', self)
        settings_action.triggered.connect(self.show_settings_dialog)
        tools_menu.addAction(settings_action)
        
        # Help menu
        help_menu = menubar.addMenu('مساعدة')
        
        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """Setup toolbar"""
        toolbar = self.addToolBar('الأدوات الرئيسية')
        
        # Add camera action
        add_camera_action = QAction('إضافة كاميرا', self)
        add_camera_action.triggered.connect(self.show_add_camera_dialog)
        toolbar.addAction(add_camera_action)
        
        toolbar.addSeparator()
        
        # Record all action
        record_all_action = QAction('تسجيل الكل', self)
        record_all_action.triggered.connect(self.record_all_cameras)
        toolbar.addAction(record_all_action)
        
        # Stop all action
        stop_all_action = QAction('إيقاف الكل', self)
        stop_all_action.triggered.connect(self.stop_all_recordings)
        toolbar.addAction(stop_all_action)
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage('جاهز')
    
    def setup_connections(self):
        """Setup signal connections"""
        try:
            if hasattr(self, 'alert_manager'):
                self.alert_manager.alert_triggered.connect(self.handle_alert)
            
            if hasattr(self, 'motion_manager'):
                self.motion_manager.add_motion_callback(self.handle_motion_detected)
        except Exception as e:
            print(f"تحذير: خطأ في ربط الإشارات: {e}")
    
    def apply_theme(self):
        """Apply dark theme"""
        try:
            if hasattr(self, 'settings'):
                theme_settings = self.settings.apply_theme_settings()
                self.setStyleSheet(theme_settings['style_sheet'])
                self.setWindowOpacity(theme_settings['opacity'])
        except Exception as e:
            print(f"تحذير: خطأ في تطبيق المظهر: {e}")
    
    def show_login_dialog(self):
        """Show login dialog"""
        try:
            from ui.login_dialog import LoginDialog
            dialog = LoginDialog(self.user_manager, self)
            if dialog.exec_() == QDialog.Accepted:
                self.current_user = self.user_manager.get_current_user()
                self.update_user_interface()
        except Exception as e:
            print(f"خطأ في تسجيل الدخول: {e}")
    
    def logout(self):
        """Logout current user"""
        try:
            if hasattr(self, 'user_manager'):
                self.user_manager.logout()
            self.current_user = None
            self.update_user_interface()
        except Exception as e:
            print(f"خطأ في تسجيل الخروج: {e}")
    
    def update_user_interface(self):
        """Update UI based on user permissions"""
        try:
            if self.current_user:
                self.user_info_label.setText(f"المستخدم: {self.current_user['username']}")
            else:
                self.user_info_label.setText("المستخدم: غير مسجل")
        except Exception as e:
            print(f"خطأ في تحديث الواجهة: {e}")
    
    def update_status(self):
        """Update status information"""
        try:
            if hasattr(self, 'camera_manager'):
                cameras = self.camera_manager.get_cameras()
                self.cameras_count_label.setText(f"عدد الكاميرات: {len(cameras)}")
            
            if hasattr(self, 'recording_manager'):
                active_recordings = len(self.recording_manager.active_recordings)
                self.recordings_count_label.setText(f"التسجيلات النشطة: {active_recordings}")
        except Exception as e:
            pass  # Silent fail for status updates
    
    def handle_alert(self, alert_type, message, data):
        """Handle alert from alert manager"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            alert_text = f"[{timestamp}] {message}"
            self.alerts_list.addItem(alert_text)
            self.alerts_list.scrollToBottom()
            self.status_bar.showMessage(message, 5000)
        except Exception as e:
            print(f"خطأ في معالجة التنبيه: {e}")
    
    def handle_motion_detected(self, camera_id, confidence, frame, snapshot_path):
        """Handle motion detection"""
        try:
            if camera_id in self.camera_widgets:
                self.camera_widgets[camera_id].set_motion_status(True)
            
            camera_name = f"Camera {camera_id}"
            if hasattr(self, 'alert_manager'):
                self.alert_manager.motion_detected(camera_id, camera_name, confidence, snapshot_path)
        except Exception as e:
            print(f"خطأ في معالجة كشف الحركة: {e}")
    
    def show_add_camera_dialog(self):
        """Show add camera dialog"""
        try:
            from ui.add_camera_dialog import AddCameraDialog
            dialog = AddCameraDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                camera_data = dialog.get_camera_data()
                if camera_data:
                    self.add_camera_to_system(camera_data)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة إضافة الكاميرا:\n{str(e)}")

    def show_settings_dialog(self):
        """Show settings dialog"""
        try:
            from ui.settings_dialog import SettingsDialog
            dialog = SettingsDialog(self.settings, self)
            dialog.settings_changed.connect(self.on_settings_changed)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الإعدادات:\n{str(e)}")

    def add_camera_to_system(self, camera_data):
        """إضافة كاميرا جديدة للنظام"""
        try:
            # تحويل البيانات لتتوافق مع قاعدة البيانات
            db_camera_data = self.convert_camera_data_for_db(camera_data)

            # إضافة الكاميرا لمدير الكاميرات
            if hasattr(self, 'camera_manager'):
                camera_id = self.camera_manager.add_camera(db_camera_data)

                # إضافة الكاميرا للقائمة
                self.camera_list.addItem(f"{camera_data['name']} - {camera_data['type']}")

                # تحديث عدد الكاميرات
                self.update_status()

                QMessageBox.information(self, "تم الإضافة", f"تم إضافة الكاميرا '{camera_data['name']}' بنجاح!")
            else:
                QMessageBox.warning(self, "تحذير", "مدير الكاميرات غير متوفر")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الكاميرا:\n{str(e)}")

    def convert_camera_data_for_db(self, camera_data):
        """تحويل بيانات الكاميرا لتتوافق مع قاعدة البيانات"""
        try:
            # استخراج IP والمنفذ من URL
            url = camera_data.get('url', '')
            ip_address = ''
            port = 554  # المنفذ الافتراضي لـ RTSP

            if camera_data['type'] == 'USB Camera':
                ip_address = 'localhost'
                port = 0
            elif '://' in url:
                # استخراج IP والمنفذ من URL
                parts = url.split('://')
                if len(parts) > 1:
                    address_part = parts[1].split('/')[0]
                    if '@' in address_part:
                        address_part = address_part.split('@')[1]

                    if ':' in address_part:
                        ip_address, port_str = address_part.split(':')
                        try:
                            port = int(port_str)
                        except:
                            port = 554
                    else:
                        ip_address = address_part
            else:
                # إذا كان URL مجرد IP
                if ':' in url:
                    ip_address, port_str = url.split(':')
                    try:
                        port = int(port_str)
                    except:
                        port = 554
                else:
                    ip_address = url

            # بناء البيانات المتوافقة مع قاعدة البيانات
            db_data = {
                'name': camera_data.get('name', ''),
                'ip_address': ip_address,
                'port': port,
                'username': camera_data.get('username', ''),
                'password': camera_data.get('password', ''),
                'rtsp_url': url,
                'location': camera_data.get('description', ''),
                'motion_detection': camera_data.get('motion_detect', True),
                'recording_enabled': camera_data.get('auto_record', True)
            }

            return db_data

        except Exception as e:
            print(f"خطأ في تحويل بيانات الكاميرا: {e}")
            # إرجاع بيانات افتراضية في حالة الخطأ
            return {
                'name': camera_data.get('name', 'Unknown Camera'),
                'ip_address': 'localhost',
                'port': 554,
                'username': camera_data.get('username', ''),
                'password': camera_data.get('password', ''),
                'rtsp_url': camera_data.get('url', ''),
                'location': camera_data.get('description', ''),
                'motion_detection': True,
                'recording_enabled': True
            }

    def on_settings_changed(self):
        """معالجة تغيير الإعدادات"""
        try:
            # إعادة تطبيق المظهر
            self.apply_theme()

            # تحديث الواجهة حسب الإعدادات الجديدة
            self.update_user_interface()

        except Exception as e:
            print(f"خطأ في تطبيق الإعدادات الجديدة: {e}")
    
    def show_about_dialog(self):
        """Show about dialog"""
        QMessageBox.about(self, "حول البرنامج", 
                         "SmartPSS Pro - نظام المراقبة الذكي\nإصدار 2.0\n\nنظام مراقبة متقدم مع كشف الحركة والتسجيل التلقائي")
    
    def record_all_cameras(self):
        """Start recording all cameras"""
        try:
            if hasattr(self, 'recording_manager'):
                for camera_id in self.camera_widgets.keys():
                    self.recording_manager.start_manual_recording(camera_id)
        except Exception as e:
            print(f"خطأ في بدء التسجيل: {e}")
    
    def stop_all_recordings(self):
        """Stop all active recordings"""
        try:
            if hasattr(self, 'recording_manager'):
                self.recording_manager.stop_all_recordings()
        except Exception as e:
            print(f"خطأ في إيقاف التسجيل: {e}")
    
    def closeEvent(self, event):
        """Handle window close event"""
        try:
            if hasattr(self, 'camera_manager'):
                self.camera_manager.stop_all_streams()
            if hasattr(self, 'motion_manager'):
                self.motion_manager.stop_all_detection()
            if hasattr(self, 'recording_manager'):
                self.recording_manager.cleanup()
            if hasattr(self, 'alert_manager'):
                self.alert_manager.stop()
        except Exception as e:
            print(f"خطأ في إغلاق النافذة: {e}")
        
        event.accept()
