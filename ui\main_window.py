#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Main Window UI for SmartPSS Pro
واجهة النافذة الرئيسية المحسنة لنظام SmartPSS Pro
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                           QGridLayout, QPushButton, QLabel, QFrame, QSplitter,
                           QMenuBar, QMenu, QAction, QStatusBar, QToolBar,
                           QScrollArea, QGroupBox, QListWidget, QTabWidget,
                           QMessageBox, QDialog, QApplication, QDockWidget,
                           QProgressBar, QSlider, QComboBox, QSpinBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QSettings, QSize
from PyQt5.QtGui import (QFont, QPixmap, QIcon, QPalette, QColor, QKeySequence,
                        QMovie, QCursor)

# Import our enhanced modules
try:
    from camera_manager import CameraManager
    from motion_detector import MotionManager
    from recorder import RecordingManager
    from user_manager import UserManager
    from settings import SettingsManager
    from alerts import AlertManager
    from database import DatabaseManager
except ImportError as e:
    print(f"خطأ في استيراد الوحدات الأساسية: {e}")

# استيراد الوحدات الاختيارية
try:
    from advanced_config import advanced_config
except ImportError:
    advanced_config = None
    print("تحذير: لم يتم العثور على advanced_config")

try:
    from ui.dashboard import DashboardWidget
except ImportError:
    DashboardWidget = None
    print("تحذير: لم يتم العثور على DashboardWidget")

class CameraWidget(QFrame):
    """Individual camera display widget"""
    
    def __init__(self, camera_id, camera_name):
        super().__init__()
        self.camera_id = camera_id
        self.camera_name = camera_name
        self.current_pixmap = None
        
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """Setup camera widget UI"""
        layout = QVBoxLayout()
        
        # Camera name label
        self.name_label = QLabel(self.camera_name)
        self.name_label.setAlignment(Qt.AlignCenter)
        self.name_label.setStyleSheet("font-weight: bold; color: white; background-color: rgba(0,0,0,0.7); padding: 5px;")
        
        # Video display label
        self.video_label = QLabel("لا يوجد إشارة")  # No Signal
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setMinimumSize(320, 240)
        self.video_label.setStyleSheet("border: 2px solid #555; background-color: #333;")
        
        # Status indicators
        status_layout = QHBoxLayout()
        
        self.recording_indicator = QLabel("●")
        self.recording_indicator.setStyleSheet("color: red; font-size: 16px;")
        self.recording_indicator.hide()
        
        self.motion_indicator = QLabel("⚡")
        self.motion_indicator.setStyleSheet("color: yellow; font-size: 16px;")
        self.motion_indicator.hide()
        
        self.connection_indicator = QLabel("●")
        self.connection_indicator.setStyleSheet("color: green; font-size: 16px;")
        
        status_layout.addWidget(QLabel("تسجيل:"))  # Recording:
        status_layout.addWidget(self.recording_indicator)
        status_layout.addWidget(QLabel("حركة:"))  # Motion:
        status_layout.addWidget(self.motion_indicator)
        status_layout.addWidget(QLabel("اتصال:"))  # Connection:
        status_layout.addWidget(self.connection_indicator)
        status_layout.addStretch()
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.record_button = QPushButton("تسجيل")  # Record
        self.record_button.setMaximumWidth(60)
        
        self.snapshot_button = QPushButton("لقطة")  # Snapshot
        self.snapshot_button.setMaximumWidth(60)
        
        button_layout.addWidget(self.record_button)
        button_layout.addWidget(self.snapshot_button)
        button_layout.addStretch()
        
        # Add to main layout
        layout.addWidget(self.name_label)
        layout.addWidget(self.video_label)
        layout.addLayout(status_layout)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def setup_style(self):
        """Setup widget styling"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                border: 2px solid #555;
                border-radius: 5px;
                background-color: #2b2b2b;
                margin: 2px;
            }
            QPushButton {
                background-color: #404040;
                border: 1px solid #666;
                padding: 3px;
                border-radius: 3px;
                color: white;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QLabel {
                color: white;
            }
        """)
    
    def update_frame(self, pixmap):
        """Update video frame"""
        if pixmap:
            # Scale pixmap to fit label
            scaled_pixmap = pixmap.scaled(
                self.video_label.size(), 
                Qt.KeepAspectRatio, 
                Qt.SmoothTransformation
            )
            self.video_label.setPixmap(scaled_pixmap)
            self.current_pixmap = pixmap
    
    def set_recording_status(self, recording):
        """Update recording indicator"""
        if recording:
            self.recording_indicator.show()
            self.record_button.setText("إيقاف")  # Stop
        else:
            self.recording_indicator.hide()
            self.record_button.setText("تسجيل")  # Record
    
    def set_motion_status(self, motion_detected):
        """Update motion indicator"""
        if motion_detected:
            self.motion_indicator.show()
        else:
            self.motion_indicator.hide()
    
    def set_connection_status(self, connected):
        """Update connection indicator"""
        if connected:
            self.connection_indicator.setStyleSheet("color: green; font-size: 16px;")
            self.video_label.setText("")
        else:
            self.connection_indicator.setStyleSheet("color: red; font-size: 16px;")
            self.video_label.setText("انقطع الاتصال")  # Connection Lost
            self.video_label.setPixmap(QPixmap())

class EnhancedMainWindow(QMainWindow):
    """النافذة الرئيسية المحسنة للتطبيق"""

    # إشارات مخصصة
    camera_added = pyqtSignal(dict)
    camera_removed = pyqtSignal(int)
    recording_started = pyqtSignal(int)
    recording_stopped = pyqtSignal(int)
    motion_detected = pyqtSignal(int, float)
    performance_warning = pyqtSignal(str)

    def __init__(self):
        super().__init__()

        # تهيئة المدراء المحسنة
        self.db = DatabaseManager()
        self.user_manager = UserManager()
        self.settings = SettingsManager()
        self.camera_manager = CameraManager()
        self.motion_manager = MotionManager()
        self.recording_manager = RecordingManager()
        self.alert_manager = AlertManager()

        # مكونات الواجهة المحسنة
        self.camera_widgets = {}
        self.current_user = None
        self.dashboard_widget = None
        self.performance_data = {}
        self.is_fullscreen = False

        # إعدادات النافذة
        self.setup_window_properties()

        # إعداد الواجهة المحسنة
        self.setup_enhanced_ui()
        self.setup_enhanced_connections()
        self.apply_enhanced_theme()

        # بدء المدراء
        self.start_managers()

        # مؤقتات التحديث
        self.setup_timers()

        # تحميل الإعدادات المحفوظة
        self.load_window_settings()

    def setup_window_properties(self):
        """إعداد خصائص النافذة"""
        self.setWindowTitle("SmartPSS Pro - نظام المراقبة الذكي المتطور")
        self.setMinimumSize(1200, 800)

        # تعيين أيقونة النافذة
        icon_path = Path("resources/icons/app_icon.png")
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))

        # إعداد الحالة الافتراضية
        self.setWindowState(Qt.WindowMaximized)

        # تفعيل قبول الملفات المسحوبة
        self.setAcceptDrops(True)

    def setup_timers(self):
        """إعداد المؤقتات"""
        # مؤقت التحديث الرئيسي
        self.main_update_timer = QTimer()
        self.main_update_timer.timeout.connect(self.update_main_status)
        self.main_update_timer.start(1000)  # كل ثانية

        # مؤقت تحديث الأداء
        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self.update_performance_info)
        self.performance_timer.start(5000)  # كل 5 ثوانٍ

        # مؤقت الحفظ التلقائي
        self.autosave_timer = QTimer()
        self.autosave_timer.timeout.connect(self.auto_save_settings)
        self.autosave_timer.start(300000)  # كل 5 دقائق

    def start_managers(self):
        """بدء تشغيل المدراء"""
        try:
            # بدء مدير التنبيهات
            if hasattr(self.alert_manager, 'start'):
                self.alert_manager.start()

            # بدء مدير كشف الحركة
            if hasattr(self.motion_manager, 'start'):
                self.motion_manager.start()

            print("تم بدء تشغيل جميع المدراء بنجاح")

        except Exception as e:
            print(f"خطأ في بدء المدراء: {e}")

    def load_window_settings(self):
        """تحميل إعدادات النافذة المحفوظة"""
        try:
            settings = QSettings()

            # استعادة حجم وموقع النافذة
            if settings.contains("window_geometry"):
                self.restoreGeometry(settings.value("window_geometry"))

            if settings.contains("window_state"):
                self.restoreState(settings.value("window_state"))

            # استعادة إعدادات أخرى
            if settings.contains("camera_grid_layout"):
                grid_layout = settings.value("camera_grid_layout", "2x2")
                self.set_camera_grid_layout(grid_layout)

        except Exception as e:
            print(f"خطأ في تحميل إعدادات النافذة: {e}")

    def save_window_settings(self):
        """حفظ إعدادات النافذة"""
        try:
            settings = QSettings()
            settings.setValue("window_geometry", self.saveGeometry())
            settings.setValue("window_state", self.saveState())

            # حفظ إعدادات إضافية
            if hasattr(self, 'current_grid_layout'):
                settings.setValue("camera_grid_layout", self.current_grid_layout)

        except Exception as e:
            print(f"خطأ في حفظ إعدادات النافذة: {e}")

    def setup_enhanced_ui(self):
        """إعداد الواجهة المحسنة"""
        # إنشاء الودجة المركزية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # شريط الأدوات العلوي المحسن
        self.setup_enhanced_toolbar()

        # المحتوى الرئيسي مع تبويبات
        self.setup_main_content()

        # شريط الحالة المحسن
        self.setup_enhanced_status_bar()

        central_widget.setLayout(main_layout)

        # إعداد القوائم المحسنة
        self.setup_enhanced_menu_bar()

        # إعداد النوافذ الجانبية
        self.setup_dock_widgets()

        # إعداد اختصارات لوحة المفاتيح
        self.setup_keyboard_shortcuts()

# للتوافق مع الكود القديم
MainWindow = EnhancedMainWindow

    def setup_main_content(self):
        """إعداد المحتوى الرئيسي"""
        # إنشاء تبويبات رئيسية
        self.main_tabs = QTabWidget()
        self.main_tabs.setTabPosition(QTabWidget.North)
        self.main_tabs.setMovable(True)
        self.main_tabs.setTabsClosable(False)

        # تبويب المراقبة المباشرة
        self.live_monitoring_tab = self.create_live_monitoring_tab()
        self.main_tabs.addTab(self.live_monitoring_tab, "🎥 المراقبة المباشرة")

        # تبويب لوحة المعلومات
        if DashboardWidget is not None:
            self.dashboard_widget = DashboardWidget()
            self.main_tabs.addTab(self.dashboard_widget, "📊 لوحة المعلومات")
        else:
            # إنشاء لوحة معلومات بسيطة
            simple_dashboard = QWidget()
            simple_layout = QVBoxLayout()
            simple_layout.addWidget(QLabel("لوحة المعلومات - قيد التطوير"))
            simple_dashboard.setLayout(simple_layout)
            self.main_tabs.addTab(simple_dashboard, "📊 لوحة المعلومات")

        # تبويب التسجيلات
        try:
            self.recordings_tab = self.create_recordings_tab()
            self.main_tabs.addTab(self.recordings_tab, "📹 التسجيلات")
        except Exception as e:
            print(f"خطأ في إنشاء تبويب التسجيلات: {e}")

        # تبويب التحليلات
        try:
            self.analytics_tab = self.create_analytics_tab()
            self.main_tabs.addTab(self.analytics_tab, "📈 التحليلات")
        except Exception as e:
            print(f"خطأ في إنشاء تبويب التحليلات: {e}")

        # تبويب الإعدادات
        try:
            self.settings_tab = self.create_settings_tab()
            self.main_tabs.addTab(self.settings_tab, "⚙️ الإعدادات")
        except Exception as e:
            print(f"خطأ في إنشاء تبويب الإعدادات: {e}")

        # إضافة التبويبات للتخطيط
        layout = self.centralWidget().layout()
        layout.addWidget(self.main_tabs)

    def create_live_monitoring_tab(self):
        """إنشاء تبويب المراقبة المباشرة"""
        tab_widget = QWidget()
        layout = QHBoxLayout()

        # إنشاء مقسم للكاميرات والتحكم
        splitter = QSplitter(Qt.Horizontal)

        # لوحة الكاميرات المحسنة
        camera_panel = self.create_enhanced_camera_panel()
        splitter.addWidget(camera_panel)

        # لوحة التحكم المحسنة
        control_panel = self.create_enhanced_control_panel()
        splitter.addWidget(control_panel)

        # تعيين النسب
        splitter.setSizes([800, 300])

        layout.addWidget(splitter)
        tab_widget.setLayout(layout)

        return tab_widget

    def create_enhanced_camera_panel(self):
        """إنشاء لوحة الكاميرات المحسنة"""
        panel = QWidget()
        layout = QVBoxLayout()

        # شريط أدوات الكاميرات
        camera_toolbar = QHBoxLayout()

        # أزرار التحكم في العرض
        grid_combo = QComboBox()
        grid_combo.addItems(["1x1", "2x2", "3x3", "4x4", "2x3", "3x4"])
        grid_combo.setCurrentText("2x2")
        grid_combo.currentTextChanged.connect(self.change_camera_grid)

        fullscreen_btn = QPushButton("🔳 ملء الشاشة")
        fullscreen_btn.clicked.connect(self.toggle_fullscreen)

        snapshot_all_btn = QPushButton("📷 لقطة للكل")
        snapshot_all_btn.clicked.connect(self.take_all_snapshots)

        record_all_btn = QPushButton("🔴 تسجيل الكل")
        record_all_btn.clicked.connect(self.toggle_all_recording)

        camera_toolbar.addWidget(QLabel("تخطيط الشبكة:"))
        camera_toolbar.addWidget(grid_combo)
        camera_toolbar.addStretch()
        camera_toolbar.addWidget(snapshot_all_btn)
        camera_toolbar.addWidget(record_all_btn)
        camera_toolbar.addWidget(fullscreen_btn)

        layout.addLayout(camera_toolbar)

        # منطقة عرض الكاميرات
        self.camera_scroll = QScrollArea()
        self.camera_grid_widget = QWidget()
        self.camera_grid_layout = QGridLayout()

        self.camera_grid_widget.setLayout(self.camera_grid_layout)
        self.camera_scroll.setWidget(self.camera_grid_widget)
        self.camera_scroll.setWidgetResizable(True)

        layout.addWidget(self.camera_scroll)

        panel.setLayout(layout)
        return panel

    def create_enhanced_control_panel(self):
        """إنشاء لوحة التحكم المحسنة"""
        panel = QWidget()
        layout = QVBoxLayout()

        # تبويبات التحكم
        control_tabs = QTabWidget()

        # تبويب الكاميرات
        cameras_tab = self.create_cameras_control_tab()
        control_tabs.addTab(cameras_tab, "📹 الكاميرات")

        # تبويب التسجيل
        recording_tab = self.create_recording_control_tab()
        control_tabs.addTab(recording_tab, "🔴 التسجيل")

        # تبويب كشف الحركة
        motion_tab = self.create_motion_control_tab()
        control_tabs.addTab(motion_tab, "⚡ كشف الحركة")

        # تبويب التنبيهات
        alerts_tab = self.create_alerts_control_tab()
        control_tabs.addTab(alerts_tab, "🔔 التنبيهات")

        layout.addWidget(control_tabs)
        panel.setLayout(layout)

        return panel
    
    def setup_camera_panel(self, parent):
        """Setup camera display panel"""
        camera_widget = QWidget()
        camera_layout = QVBoxLayout()
        
        # Camera grid
        self.camera_scroll = QScrollArea()
        self.camera_grid_widget = QWidget()
        self.camera_grid_layout = QGridLayout()
        
        self.camera_grid_widget.setLayout(self.camera_grid_layout)
        self.camera_scroll.setWidget(self.camera_grid_widget)
        self.camera_scroll.setWidgetResizable(True)
        
        camera_layout.addWidget(self.camera_scroll)
        camera_widget.setLayout(camera_layout)
        
        parent.addWidget(camera_widget)
    
    def setup_control_panel(self, parent):
        """Setup control panel"""
        control_widget = QWidget()
        control_layout = QVBoxLayout()
        
        # Tab widget for different controls
        self.control_tabs = QTabWidget()
        
        # Camera management tab
        self.setup_camera_management_tab()
        
        # Recording tab
        self.setup_recording_tab()
        
        # Alerts tab
        self.setup_alerts_tab()
        
        # System info tab
        self.setup_system_info_tab()
        
        control_layout.addWidget(self.control_tabs)
        control_widget.setLayout(control_layout)
        
        parent.addWidget(control_widget)
    
    def setup_camera_management_tab(self):
        """Setup camera management tab"""
        camera_tab = QWidget()
        layout = QVBoxLayout()
        
        # Camera list
        self.camera_list = QListWidget()
        layout.addWidget(QLabel("الكاميرات:"))  # Cameras:
        layout.addWidget(self.camera_list)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        add_button = QPushButton("إضافة كاميرا")  # Add Camera
        edit_button = QPushButton("تعديل")  # Edit
        delete_button = QPushButton("حذف")  # Delete
        
        button_layout.addWidget(add_button)
        button_layout.addWidget(edit_button)
        button_layout.addWidget(delete_button)
        
        layout.addLayout(button_layout)
        layout.addStretch()
        
        camera_tab.setLayout(layout)
        self.control_tabs.addTab(camera_tab, "الكاميرات")  # Cameras
    
    def setup_recording_tab(self):
        """Setup recording control tab"""
        recording_tab = QWidget()
        layout = QVBoxLayout()
        
        # Recording controls
        record_group = QGroupBox("التحكم في التسجيل")  # Recording Control
        record_layout = QVBoxLayout()
        
        self.record_all_button = QPushButton("تسجيل جميع الكاميرات")  # Record All Cameras
        self.stop_all_button = QPushButton("إيقاف جميع التسجيلات")  # Stop All Recordings
        
        record_layout.addWidget(self.record_all_button)
        record_layout.addWidget(self.stop_all_button)
        record_group.setLayout(record_layout)
        
        # Recording list
        recordings_group = QGroupBox("التسجيلات الحديثة")  # Recent Recordings
        recordings_layout = QVBoxLayout()
        
        self.recordings_list = QListWidget()
        recordings_layout.addWidget(self.recordings_list)
        
        recordings_group.setLayout(recordings_layout)
        
        layout.addWidget(record_group)
        layout.addWidget(recordings_group)
        
        recording_tab.setLayout(layout)
        self.control_tabs.addTab(recording_tab, "التسجيل")  # Recording
    
    def setup_alerts_tab(self):
        """Setup alerts tab"""
        alerts_tab = QWidget()
        layout = QVBoxLayout()
        
        # Alert list
        self.alerts_list = QListWidget()
        layout.addWidget(QLabel("التنبيهات الحديثة:"))  # Recent Alerts:
        layout.addWidget(self.alerts_list)
        
        # Clear button
        clear_button = QPushButton("مسح التنبيهات")  # Clear Alerts
        layout.addWidget(clear_button)
        
        alerts_tab.setLayout(layout)
        self.control_tabs.addTab(alerts_tab, "التنبيهات")  # Alerts
    
    def setup_system_info_tab(self):
        """Setup system information tab"""
        info_tab = QWidget()
        layout = QVBoxLayout()
        
        # System status
        self.system_status_label = QLabel("حالة النظام: جاهز")  # System Status: Ready
        self.user_info_label = QLabel("المستخدم: غير مسجل")  # User: Not logged in
        self.cameras_count_label = QLabel("عدد الكاميرات: 0")  # Camera count: 0
        self.recordings_count_label = QLabel("التسجيلات النشطة: 0")  # Active recordings: 0
        
        layout.addWidget(self.system_status_label)
        layout.addWidget(self.user_info_label)
        layout.addWidget(self.cameras_count_label)
        layout.addWidget(self.recordings_count_label)
        layout.addStretch()
        
        info_tab.setLayout(layout)
        self.control_tabs.addTab(info_tab, "معلومات النظام")  # System Info
    
    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('ملف')  # File
        
        login_action = QAction('تسجيل الدخول', self)  # Login
        login_action.triggered.connect(self.show_login_dialog)
        file_menu.addAction(login_action)
        
        logout_action = QAction('تسجيل الخروج', self)  # Logout
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('خروج', self)  # Exit
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu('عرض')  # View
        
        # Tools menu
        tools_menu = menubar.addMenu('أدوات')  # Tools
        
        settings_action = QAction('الإعدادات', self)  # Settings
        settings_action.triggered.connect(self.show_settings_dialog)
        tools_menu.addAction(settings_action)
        
        # Help menu
        help_menu = menubar.addMenu('مساعدة')  # Help
        
        about_action = QAction('حول البرنامج', self)  # About
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """Setup toolbar"""
        toolbar = self.addToolBar('الأدوات الرئيسية')  # Main Tools
        
        # Add camera action
        add_camera_action = QAction('إضافة كاميرا', self)  # Add Camera
        add_camera_action.triggered.connect(self.show_add_camera_dialog)
        toolbar.addAction(add_camera_action)
        
        toolbar.addSeparator()
        
        # Record all action
        record_all_action = QAction('تسجيل الكل', self)  # Record All
        record_all_action.triggered.connect(self.record_all_cameras)
        toolbar.addAction(record_all_action)
        
        # Stop all action
        stop_all_action = QAction('إيقاف الكل', self)  # Stop All
        stop_all_action.triggered.connect(self.stop_all_recordings)
        toolbar.addAction(stop_all_action)
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage('جاهز')  # Ready
    
    def setup_connections(self):
        """Setup signal connections"""
        # Alert manager connections
        self.alert_manager.alert_triggered.connect(self.handle_alert)
        
        # Motion manager connections
        self.motion_manager.add_motion_callback(self.handle_motion_detected)
    
    def apply_theme(self):
        """Apply dark theme"""
        theme_settings = self.settings.apply_theme_settings()
        self.setStyleSheet(theme_settings['style_sheet'])
        self.setWindowOpacity(theme_settings['opacity'])
    
    def show_login_dialog(self):
        """Show login dialog"""
        from ui.login_dialog import LoginDialog
        dialog = LoginDialog(self.user_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            self.current_user = self.user_manager.get_current_user()
            self.update_user_interface()
    
    def logout(self):
        """Logout current user"""
        self.user_manager.logout()
        self.current_user = None
        self.update_user_interface()
    
    def update_user_interface(self):
        """Update UI based on user permissions"""
        if self.current_user:
            self.user_info_label.setText(f"المستخدم: {self.current_user['username']}")
            # Enable/disable features based on permissions
        else:
            self.user_info_label.setText("المستخدم: غير مسجل")
    
    def update_status(self):
        """Update status information"""
        # Update camera count
        cameras = self.camera_manager.get_cameras()
        self.cameras_count_label.setText(f"عدد الكاميرات: {len(cameras)}")
        
        # Update active recordings count
        active_recordings = len(self.recording_manager.active_recordings)
        self.recordings_count_label.setText(f"التسجيلات النشطة: {active_recordings}")
    
    def handle_alert(self, alert_type, message, data):
        """Handle alert from alert manager"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        alert_text = f"[{timestamp}] {message}"
        self.alerts_list.addItem(alert_text)
        
        # Scroll to bottom
        self.alerts_list.scrollToBottom()
        
        # Update status bar
        self.status_bar.showMessage(message, 5000)
    
    def handle_motion_detected(self, camera_id, confidence, frame, snapshot_path):
        """Handle motion detection"""
        # Update camera widget
        if camera_id in self.camera_widgets:
            self.camera_widgets[camera_id].set_motion_status(True)
        
        # Trigger alert
        camera_name = f"Camera {camera_id}"  # Get actual name from database
        self.alert_manager.motion_detected(camera_id, camera_name, confidence, snapshot_path)
    
    def show_add_camera_dialog(self):
        """Show add camera dialog"""
        # Implementation would show camera configuration dialog
        pass
    
    def show_settings_dialog(self):
        """Show settings dialog"""
        # Implementation would show settings dialog
        pass
    
    def show_about_dialog(self):
        """Show about dialog"""
        QMessageBox.about(self, "حول البرنامج", 
                         "SmartPSS - نظام المراقبة الذكي\nإصدار 1.0\n\nنظام مراقبة متقدم مع كشف الحركة والتسجيل التلقائي")
    
    def record_all_cameras(self):
        """Start recording all cameras"""
        for camera_id in self.camera_widgets.keys():
            self.recording_manager.start_manual_recording(camera_id)
    
    def stop_all_recordings(self):
        """Stop all active recordings"""
        self.recording_manager.stop_all_recordings()
    
    def closeEvent(self, event):
        """Handle window close event"""
        # Stop all managers
        self.camera_manager.stop_all_streams()
        self.motion_manager.stop_all_detection()
        self.recording_manager.cleanup()
        self.alert_manager.stop()

        event.accept()

    # الوظائف المفقودة للنافذة المحسنة
    def create_cameras_control_tab(self):
        """إنشاء تبويب تحكم الكاميرات"""
        tab_widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("تحكم الكاميرات - قيد التطوير"))
        tab_widget.setLayout(layout)
        return tab_widget

    def create_recording_control_tab(self):
        """إنشاء تبويب تحكم التسجيل"""
        tab_widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("تحكم التسجيل - قيد التطوير"))
        tab_widget.setLayout(layout)
        return tab_widget

    def create_motion_control_tab(self):
        """إنشاء تبويب تحكم كشف الحركة"""
        tab_widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("تحكم كشف الحركة - قيد التطوير"))
        tab_widget.setLayout(layout)
        return tab_widget

    def create_alerts_control_tab(self):
        """إنشاء تبويب تحكم التنبيهات"""
        tab_widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("تحكم التنبيهات - قيد التطوير"))
        tab_widget.setLayout(layout)
        return tab_widget

    def setup_enhanced_toolbar(self):
        """إعداد شريط الأدوات المحسن"""
        pass  # سيتم تنفيذها لاحقاً

    def setup_enhanced_menu_bar(self):
        """إعداد شريط القوائم المحسن"""
        pass  # سيتم تنفيذها لاحقاً

    def setup_enhanced_status_bar(self):
        """إعداد شريط الحالة المحسن"""
        pass  # سيتم تنفيذها لاحقاً

    def setup_dock_widgets(self):
        """إعداد النوافذ الجانبية"""
        pass  # سيتم تنفيذها لاحقاً

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        pass  # سيتم تنفيذها لاحقاً

    def setup_enhanced_connections(self):
        """إعداد الاتصالات المحسنة"""
        pass  # سيتم تنفيذها لاحقاً

    def apply_enhanced_theme(self):
        """تطبيق المظهر المحسن"""
        pass  # سيتم تنفيذها لاحقاً

    def change_camera_grid(self, grid_layout):
        """تغيير تخطيط شبكة الكاميرات"""
        pass  # سيتم تنفيذها لاحقاً

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        pass  # سيتم تنفيذها لاحقاً

    def take_all_snapshots(self):
        """أخذ لقطات لجميع الكاميرات"""
        pass  # سيتم تنفيذها لاحقاً

    def toggle_all_recording(self):
        """تبديل تسجيل جميع الكاميرات"""
        pass  # سيتم تنفيذها لاحقاً

    def set_camera_grid_layout(self, layout):
        """تعيين تخطيط شبكة الكاميرات"""
        pass  # سيتم تنفيذها لاحقاً

    def update_main_status(self):
        """تحديث الحالة الرئيسية"""
        pass  # سيتم تنفيذها لاحقاً

    def update_performance_info(self, performance_data=None):
        """تحديث معلومات الأداء"""
        pass  # سيتم تنفيذها لاحقاً

    def show_performance_warning(self, warning_message):
        """عرض تحذير الأداء"""
        pass  # سيتم تنفيذها لاحقاً

    def auto_save_settings(self):
        """حفظ تلقائي للإعدادات"""
        pass  # سيتم تنفيذها لاحقاً

    def create_recordings_tab(self):
        """إنشاء تبويب التسجيلات"""
        tab_widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("تبويب التسجيلات - قيد التطوير"))
        tab_widget.setLayout(layout)
        return tab_widget

    def create_analytics_tab(self):
        """إنشاء تبويب التحليلات"""
        tab_widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("تبويب التحليلات - قيد التطوير"))
        tab_widget.setLayout(layout)
        return tab_widget

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        tab_widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("تبويب الإعدادات - قيد التطوير"))
        tab_widget.setLayout(layout)
        return tab_widget

    def setup_dock_widgets(self):
        """إعداد النوافذ الجانبية"""
        pass  # سيتم تنفيذها لاحقاً

    def setup_keyboard_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        pass  # سيتم تنفيذها لاحقاً

    def setup_enhanced_connections(self):
        """إعداد الاتصالات المحسنة"""
        pass  # سيتم تنفيذها لاحقاً

    def apply_enhanced_theme(self):
        """تطبيق المظهر المحسن"""
        pass  # سيتم تنفيذها لاحقاً

    def change_camera_grid(self, grid_layout):
        """تغيير تخطيط شبكة الكاميرات"""
        pass  # سيتم تنفيذها لاحقاً

    def toggle_fullscreen(self):
        """تبديل وضع ملء الشاشة"""
        pass  # سيتم تنفيذها لاحقاً

    def take_all_snapshots(self):
        """أخذ لقطات لجميع الكاميرات"""
        pass  # سيتم تنفيذها لاحقاً

    def toggle_all_recording(self):
        """تبديل تسجيل جميع الكاميرات"""
        pass  # سيتم تنفيذها لاحقاً

    def set_camera_grid_layout(self, layout):
        """تعيين تخطيط شبكة الكاميرات"""
        pass  # سيتم تنفيذها لاحقاً

    def update_main_status(self):
        """تحديث الحالة الرئيسية"""
        pass  # سيتم تنفيذها لاحقاً

    def update_performance_info(self, performance_data=None):
        """تحديث معلومات الأداء"""
        pass  # سيتم تنفيذها لاحقاً

    def show_performance_warning(self, warning_message):
        """عرض تحذير الأداء"""
        pass  # سيتم تنفيذها لاحقاً

    def auto_save_settings(self):
        """حفظ تلقائي للإعدادات"""
        pass  # سيتم تنفيذها لاحقاً
