@echo off
title SmartPSS Pro - Smart Monitoring System
color 0A

echo.
echo ========================================
echo    SmartPSS Pro - Smart Monitoring System
echo    تم إصلاح جميع المشاكل!
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed!
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo [INFO] Python is available
python --version
echo.

echo [SUCCESS] All issues have been fixed!
echo [INFO] Choose your preferred mode:
echo.
echo [1] Working Version (Recommended - Fully Functional UI)
echo [2] Safe Diagnostic Mode (System Check and Repair)
echo [3] Minimal Test (Basic PyQt5 Test)
echo [4] Advanced Mode (Full System with All Features)
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo.
    echo [INFO] Starting SmartPSS Pro Working Version...
    echo [INFO] This version has a complete UI with tabs and controls
    python working_main.py
) else if "%choice%"=="2" (
    echo.
    echo [INFO] Starting Safe Diagnostic Mode...
    echo [INFO] This mode will check your system and fix issues
    python safe_main.py
) else if "%choice%"=="3" (
    echo.
    echo [INFO] Starting Minimal Test...
    echo [INFO] This will test if PyQt5 is working properly
    python minimal_test.py
) else if "%choice%"=="4" (
    echo.
    echo [INFO] Starting Advanced Mode...
    echo [INFO] This includes all advanced features
    python main.py
) else (
    echo.
    echo [INFO] Invalid choice, starting Working Version...
    python working_main.py
)

echo.
if errorlevel 1 (
    echo [ERROR] SmartPSS Pro exited with an error
    echo [INFO] Try running Safe Diagnostic Mode (option 2)
    pause
) else (
    echo [SUCCESS] SmartPSS Pro closed normally
    echo [INFO] Thank you for using SmartPSS Pro!
)

echo.
echo Press any key to exit...
pause >nul
