#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                           QLineEdit, QPushButton, QFrame, QWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPalette, QColor, QPainter, QLinearGradient, QBrush

class ModernLoginDialog(QDialog):
    """نافذة تسجيل دخول عصرية مثل الصورة"""
    
    login_successful = pyqtSignal(dict)
    
    def __init__(self, user_manager, parent=None):
        super().__init__(parent)
        self.user_manager = user_manager
        self.setup_ui()
        self.setup_style()
        
    def setup_ui(self):
        """إعداد واجهة عصرية"""
        self.setWindowTitle("Sign In")
        self.setFixedSize(450, 350)
        self.setModal(True)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        
        # الحاوية الرئيسية
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إطار النافذة مع الحدود الذهبية
        self.frame = QFrame()
        self.frame.setObjectName("mainFrame")
        
        # تخطيط المحتوى
        content_layout = QVBoxLayout(self.frame)
        content_layout.setContentsMargins(40, 40, 40, 40)
        content_layout.setSpacing(25)
        
        # العنوان والأيقونة
        header_layout = QHBoxLayout()
        
        title_label = QLabel("Sign In")
        title_label.setObjectName("titleLabel")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        
        # أيقونة القفل
        lock_icon = QLabel("🔒")
        lock_icon.setObjectName("lockIcon")
        lock_icon.setFont(QFont("Arial", 20))
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(lock_icon)
        
        content_layout.addLayout(header_layout)
        content_layout.addSpacing(20)
        
        # حقل User Id
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("User Id")
        self.username_edit.setText("admin")
        self.username_edit.setObjectName("inputField")
        self.username_edit.setFixedHeight(50)
        self.username_edit.setFixedWidth(300)

        # حاوية لتوسيط حقل المستخدم
        username_container = QHBoxLayout()
        username_container.addStretch()
        username_container.addWidget(self.username_edit)
        username_container.addStretch()
        content_layout.addLayout(username_container)

        # حقل Password
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("Password")
        self.password_edit.setText("admin")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setObjectName("inputField")
        self.password_edit.setFixedHeight(50)
        self.password_edit.setFixedWidth(300)

        # حاوية لتوسيط حقل كلمة المرور
        password_container = QHBoxLayout()
        password_container.addStretch()
        password_container.addWidget(self.password_edit)
        password_container.addStretch()
        content_layout.addLayout(password_container)
        
        # رابط Forgot Password
        forgot_label = QLabel("Forgot Password?")
        forgot_label.setObjectName("forgotLabel")
        forgot_label.setAlignment(Qt.AlignCenter)
        forgot_label.setFixedWidth(300)

        # حاوية لتوسيط رابط نسيان كلمة المرور
        forgot_container = QHBoxLayout()
        forgot_container.addStretch()
        forgot_container.addWidget(forgot_label)
        forgot_container.addStretch()
        content_layout.addLayout(forgot_container)
        
        content_layout.addSpacing(10)
        
        # زر Log In
        self.login_button = QPushButton("Log In")
        self.login_button.setObjectName("loginButton")
        self.login_button.setFixedHeight(50)
        self.login_button.setFixedWidth(300)

        # حاوية لتوسيط زر الدخول
        button_container = QHBoxLayout()
        button_container.addStretch()
        button_container.addWidget(self.login_button)
        button_container.addStretch()
        content_layout.addLayout(button_container)

        self.login_button.clicked.connect(self.attempt_login)
        
        # رسالة الحالة
        self.status_label = QLabel("")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignCenter)
        content_layout.addWidget(self.status_label)
        
        main_layout.addWidget(self.frame)
        self.setLayout(main_layout)
        
        # ربط Enter بتسجيل الدخول
        self.username_edit.returnPressed.connect(self.attempt_login)
        self.password_edit.returnPressed.connect(self.attempt_login)
        
        # تركيز على اسم المستخدم
        self.username_edit.setFocus()
        
    def setup_style(self):
        """تطبيق الأنماط العصرية"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e3c72, stop:1 #2a5298);
            }
            
            #mainFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(30, 60, 114, 0.9), stop:1 rgba(42, 82, 152, 0.9));
                border: 2px solid #d4af37;
                border-radius: 20px;
                margin: 20px;
            }
            
            #titleLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
            
            #lockIcon {
                color: #d4af37;
                background: rgba(212, 175, 55, 0.2);
                border: 1px solid #d4af37;
                border-radius: 15px;
                padding: 5px;
                min-width: 30px;
                min-height: 30px;
                text-align: center;
            }
            
            #inputField {
                background: rgba(255, 255, 255, 0.1);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 25px;
                padding: 15px 25px;
                color: white;
                font-size: 16px;
                font-weight: 500;
                selection-background-color: #d4af37;
            }
            
            #inputField:focus {
                border: 2px solid #d4af37;
                background: rgba(255, 255, 255, 0.15);
            }
            
            #inputField::placeholder {
                color: rgba(255, 255, 255, 0.7);
            }
            
            #forgotLabel {
                color: #87ceeb;
                font-size: 12px;
                text-decoration: underline;
            }
            
            #forgotLabel:hover {
                color: #d4af37;
            }
            
            #loginButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4a90e2, stop:1 #357abd);
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 25px;
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
            }
            
            #loginButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #5ba0f2, stop:1 #4080cd);
                border: 2px solid #d4af37;
            }
            
            #loginButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3a80d2, stop:1 #2570ad);
            }
            
            #statusLabel {
                color: #ff6b6b;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        
    def attempt_login(self):
        """محاولة تسجيل الدخول"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        if not username:
            self.status_label.setText("Please enter User Id")
            return
            
        if not password:
            self.status_label.setText("Please enter Password")
            return
        
        # محاولة المصادقة
        success, message = self.user_manager.authenticate(username, password)
        
        if success:
            self.status_label.setText("Login successful!")
            self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            
            user_info = self.user_manager.get_current_user()
            self.login_successful.emit(user_info)
            self.accept()
        else:
            self.status_label.setText(f"Error: {message}")
            self.status_label.setStyleSheet("color: #ff6b6b; font-weight: bold;")
            self.password_edit.clear()
            self.password_edit.setFocus()
            
    def mousePressEvent(self, event):
        """السماح بسحب النافذة"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
            
    def mouseMoveEvent(self, event):
        """تحريك النافذة"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()
