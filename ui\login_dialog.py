#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Login dialog for SmartPSS-like monitoring system
Arabic-supported login interface with user authentication
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                           QLineEdit, QPushButton, QLabel, QCheckBox, 
                           QMessageBox, QFrame, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QIcon

class LoginDialog(QDialog):
    """User login dialog"""
    
    login_successful = pyqtSignal(dict)  # user_info
    
    def __init__(self, user_manager, parent=None):
        super().__init__(parent)
        self.user_manager = user_manager
        self.setup_ui()
        self.setup_style()
        
    def setup_ui(self):
        """Setup login dialog UI"""
        self.setWindowTitle("تسجيل الدخول - SmartPSS Pro")  # Login - SmartPSS Pro
        self.setFixedSize(400, 350)
        self.setModal(True)

        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setSpacing(25)
        main_layout.setContentsMargins(35, 25, 35, 25)

        # Header section
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                border-radius: 10px;
                margin: 5px;
                padding: 15px;
            }
        """)
        header_layout = QVBoxLayout()

        # Title with icon
        title_label = QLabel("🔐 نظام المراقبة الذكي")  # Smart Monitoring System
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 20px; font-weight: bold; color: white; margin: 5px;")

        subtitle_label = QLabel("SmartPSS Pro")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("font-size: 16px; color: #E3F2FD; margin: 2px;")

        version_label = QLabel("الإصدار 2.1.0")  # Version 2.1.0
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("font-size: 12px; color: #BBDEFB; margin: 2px;")

        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        header_layout.addWidget(version_label)
        header_frame.setLayout(header_layout)

        # Login form
        form_group = QGroupBox("🔑 تسجيل الدخول")
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(20, 20, 20, 15)

        # Username field
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        self.username_edit.setLayoutDirection(Qt.RightToLeft)
        self.username_edit.setText("admin")
        self.username_edit.setMinimumHeight(40)
        self.username_edit.setStyleSheet("background-color: white; color: black; border: 2px solid black; padding: 10px; font-size: 14px; font-weight: bold;")

        # Password field
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setLayoutDirection(Qt.RightToLeft)
        self.password_edit.setText("admin")
        self.password_edit.setMinimumHeight(40)
        self.password_edit.setStyleSheet("background-color: white; color: black; border: 2px solid black; padding: 10px; font-size: 14px; font-weight: bold;")

        # Add fields to form
        form_layout.addRow("👤 اسم المستخدم:", self.username_edit)
        form_layout.addRow("🔒 كلمة المرور:", self.password_edit)

        # Remember me checkbox
        self.remember_checkbox = QCheckBox("🔄 تذكرني")
        self.remember_checkbox.setChecked(True)
        form_layout.addRow("", self.remember_checkbox)

        form_group.setLayout(form_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        self.login_button = QPushButton("🚀 دخول")
        self.login_button.setDefault(True)
        self.login_button.clicked.connect(self.attempt_login)
        self.login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #388E3C);
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                font-size: 14px;
                font-weight: bold;
                color: white;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #66BB6A, stop:1 #4CAF50);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2E7D32, stop:1 #1B5E20);
            }
        """)

        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #757575, stop:1 #424242);
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                font-size: 14px;
                font-weight: bold;
                color: white;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #9E9E9E, stop:1 #616161);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #212121, stop:1 #000000);
            }
        """)

        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.login_button)

        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #f44336; margin: 10px; font-size: 12px;")

        # Add to main layout
        main_layout.addWidget(header_frame)
        main_layout.addWidget(form_group)
        main_layout.addWidget(self.status_label)
        main_layout.addLayout(button_layout)
        main_layout.addStretch()
        
        self.setLayout(main_layout)
        
        # Connect Enter key to login
        self.username_edit.returnPressed.connect(self.attempt_login)
        self.password_edit.returnPressed.connect(self.attempt_login)
        
        # Set focus to username field
        self.username_edit.setFocus()
    
    def setup_style(self):
        """Setup dialog styling"""
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a1a1a, stop:1 #2b2b2b);
                color: white;
                border-radius: 15px;
                font-family: 'Segoe UI', Tahoma, Arial;
            }
            QCheckBox {
                color: white;
                font-size: 14px;
                spacing: 10px;
                font-family: 'Segoe UI', Tahoma, Arial;
            }
            QCheckBox::indicator {
                width: 22px;
                height: 22px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #666;
                background-color: #404040;
                border-radius: 5px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #2196F3;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                border-radius: 5px;
            }
            QCheckBox::indicator:hover {
                border-color: #2196F3;
                background-color: #454545;
            }
            QLabel {
                color: white;
                font-family: 'Segoe UI', Tahoma, Arial;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #666;
                border-radius: 12px;
                margin-top: 20px;
                padding-top: 20px;
                background-color: rgba(53, 53, 53, 0.8);
                font-family: 'Segoe UI', Tahoma, Arial;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 15px 0 15px;
                color: #2196F3;
                font-size: 16px;
                font-weight: bold;
            }
        """)
    
    def attempt_login(self):
        """Attempt user login"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        # Validate input
        if not username:
            self.show_error("يرجى إدخال اسم المستخدم")  # Please enter username
            self.username_edit.setFocus()
            return
        
        if not password:
            self.show_error("يرجى إدخال كلمة المرور")  # Please enter password
            self.password_edit.setFocus()
            return
        
        # Disable login button during authentication
        self.login_button.setEnabled(False)
        self.login_button.setText("⏳ جاري التحقق...")  # Verifying...
        self.status_label.setText("")
        
        # Attempt authentication
        success, message = self.user_manager.authenticate(username, password)
        
        if success:
            self.show_success("✅ تم تسجيل الدخول بنجاح")  # Login successful

            # Get user info
            user_info = self.user_manager.get_current_user()
            self.login_successful.emit(user_info)

            # Close dialog
            self.accept()
        else:
            self.show_error(f"❌ {message}")
            self.password_edit.clear()
            self.password_edit.setFocus()

        # Re-enable login button
        self.login_button.setEnabled(True)
        self.login_button.setText("🚀 دخول")  # Login
    
    def show_error(self, message):
        """Show error message"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("""
            color: #f44336;
            background-color: rgba(244, 67, 54, 0.1);
            border: 1px solid #f44336;
            border-radius: 5px;
            padding: 8px;
            margin: 5px;
            font-weight: bold;
        """)

    def show_success(self, message):
        """Show success message"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("""
            color: #4CAF50;
            background-color: rgba(76, 175, 80, 0.1);
            border: 1px solid #4CAF50;
            border-radius: 5px;
            padding: 8px;
            margin: 5px;
            font-weight: bold;
        """)
    
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)

class ChangePasswordDialog(QDialog):
    """Change password dialog"""
    
    def __init__(self, user_manager, parent=None):
        super().__init__(parent)
        self.user_manager = user_manager
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """Setup change password dialog UI"""
        self.setWindowTitle("تغيير كلمة المرور")  # Change Password
        self.setFixedSize(400, 250)
        self.setModal(True)
        
        # Main layout
        main_layout = QVBoxLayout()
        
        # Form
        form_group = QGroupBox("تغيير كلمة المرور")  # Change Password
        form_layout = QFormLayout()
        
        # Current password
        self.current_password_edit = QLineEdit()
        self.current_password_edit.setEchoMode(QLineEdit.Password)
        self.current_password_edit.setPlaceholderText("كلمة المرور الحالية")  # Current password
        form_layout.addRow("كلمة المرور الحالية:", self.current_password_edit)
        
        # New password
        self.new_password_edit = QLineEdit()
        self.new_password_edit.setEchoMode(QLineEdit.Password)
        self.new_password_edit.setPlaceholderText("كلمة المرور الجديدة")  # New password
        form_layout.addRow("كلمة المرور الجديدة:", self.new_password_edit)
        
        # Confirm password
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setPlaceholderText("تأكيد كلمة المرور")  # Confirm password
        form_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_edit)
        
        form_group.setLayout(form_layout)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.change_button = QPushButton("تغيير")  # Change
        self.change_button.clicked.connect(self.change_password)
        
        self.cancel_button = QPushButton("إلغاء")  # Cancel
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.change_button)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        
        # Add to main layout
        main_layout.addWidget(form_group)
        main_layout.addWidget(self.status_label)
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)
        
        # Set focus
        self.current_password_edit.setFocus()
    
    def setup_style(self):
        """Setup dialog styling"""
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit {
                background-color: #404040;
                border: 2px solid #555;
                border-radius: 5px;
                padding: 8px;
                font-size: 12px;
                color: white;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
            QPushButton {
                background-color: #2196F3;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-size: 12px;
                font-weight: bold;
                color: white;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
    
    def change_password(self):
        """Change user password"""
        current_password = self.current_password_edit.text()
        new_password = self.new_password_edit.text()
        confirm_password = self.confirm_password_edit.text()
        
        # Validate input
        if not current_password:
            self.show_error("يرجى إدخال كلمة المرور الحالية")  # Please enter current password
            return
        
        if not new_password:
            self.show_error("يرجى إدخال كلمة المرور الجديدة")  # Please enter new password
            return
        
        if new_password != confirm_password:
            self.show_error("كلمة المرور الجديدة غير متطابقة")  # New passwords don't match
            return
        
        if len(new_password) < 8:
            self.show_error("كلمة المرور يجب أن تكون 8 أحرف على الأقل")  # Password must be at least 8 characters
            return
        
        # Attempt password change
        success, message = self.user_manager.change_password(current_password, new_password)
        
        if success:
            self.show_success(message)
            QMessageBox.information(self, "نجح", message)  # Success
            self.accept()
        else:
            self.show_error(message)
    
    def show_error(self, message):
        """Show error message"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: #f44336; margin: 5px;")
    
    def show_success(self, message):
        """Show success message"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("color: #4CAF50; margin: 5px;")
