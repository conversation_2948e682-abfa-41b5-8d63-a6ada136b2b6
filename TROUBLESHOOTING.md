# 🔧 SmartPSS Pro - دليل حل المشاكل

## ❌ المشكلة: "فشل في عرض شاشة تسجيل الدخول"

### 🎯 الحلول المقترحة:

### 1. **استخدم الوضع الآمن (الأكثر استقراراً)**
```bash
python safe_main.py
```

### 2. **استخدم ملف التشغيل واختر الوضع 1**
```bash
run_smartpss.bat
```
ثم اختر الرقم `1` للوضع الآمن

### 3. **فحص المتطلبات يدوياً**
```bash
python -c "import PyQt5; print('PyQt5 OK')"
python -c "import numpy; print('NumPy OK')"
python -c "import psutil; print('psutil OK')"
```

### 4. **إعادة تثبيت PyQt5**
```bash
pip uninstall PyQt5
pip install PyQt5
```

## 🔍 تشخيص المشاكل

### فحص إصدار Python:
```bash
python --version
```
يجب أن يكون 3.8 أو أحدث

### فحص المكتبات المثبتة:
```bash
pip list | findstr PyQt5
pip list | findstr numpy
pip list | findstr psutil
```

### تثبيت المتطلبات الأساسية:
```bash
pip install PyQt5 numpy psutil
```

## 🚀 أوضاع التشغيل المختلفة

### 1. **الوضع الآمن** (الأكثر استقراراً)
```bash
python safe_main.py
```
- ✅ معالجة شاملة للأخطاء
- ✅ تثبيت تلقائي للمكتبات المفقودة
- ✅ واجهة مبسطة وآمنة
- ✅ اختبار المكونات بشكل منفصل

### 2. **الوضع البسيط**
```bash
python simple_main.py
```
- ✅ واجهة اختبار بسيطة
- ✅ فحص المتطلبات
- ⚠️ أقل معالجة للأخطاء

### 3. **النظام الكامل**
```bash
python main.py
```
- 🚀 جميع الميزات المتقدمة
- ⚠️ يتطلب جميع المكتبات
- ⚠️ أكثر تعقيداً

## 🔧 حلول للمشاكل الشائعة

### مشكلة: "ModuleNotFoundError: No module named 'PyQt5'"
```bash
pip install PyQt5
```

### مشكلة: "ImportError: DLL load failed"
```bash
pip uninstall PyQt5
pip install PyQt5 --force-reinstall
```

### مشكلة: "unexpected indent"
- تأكد من أن ملفات Python لم تتلف
- أعد تحميل الملفات إذا لزم الأمر

### مشكلة: النظام لا يستجيب
1. أغلق التطبيق
2. شغل الوضع الآمن: `python safe_main.py`
3. اختبر المكونات واحداً تلو الآخر

## 📋 خطوات التشخيص المنهجي

### الخطوة 1: فحص البيئة
```bash
python --version
pip --version
```

### الخطوة 2: فحص المكتبات
```bash
python -c "import sys; print(sys.path)"
python -c "import PyQt5; print('PyQt5:', PyQt5.__file__)"
```

### الخطوة 3: تشغيل الوضع الآمن
```bash
python safe_main.py
```

### الخطوة 4: اختبار المكونات
- اضغط "اختبار قاعدة البيانات"
- اضغط "اختبار الإعدادات"
- اضغط "تشغيل النظام الكامل"

## 🛠️ إعادة التثبيت الكامل

### إذا فشلت جميع الحلول:

1. **إنشاء بيئة افتراضية جديدة:**
```bash
python -m venv smartpss_new
smartpss_new\Scripts\activate
```

2. **تثبيت المتطلبات:**
```bash
pip install PyQt5 numpy psutil
```

3. **تشغيل الوضع الآمن:**
```bash
python safe_main.py
```

## 📞 الحصول على المساعدة

### معلومات مفيدة للدعم:
```bash
python --version
pip list
python -c "import platform; print(platform.platform())"
```

### ملفات السجل:
- تحقق من مجلد `logs/` للأخطاء التفصيلية
- ابحث عن ملفات `*.log` للمعلومات

## ✅ التحقق من نجاح الإصلاح

### بعد تطبيق الحلول:
1. شغل `python safe_main.py`
2. يجب أن ترى نافذة "SmartPSS Pro - الوضع الآمن"
3. اختبر كل مكون على حدة
4. إذا نجحت الاختبارات، جرب النظام الكامل

---

**💡 نصيحة:** ابدأ دائماً بالوضع الآمن للتأكد من أن النظام يعمل بشكل أساسي قبل الانتقال للميزات المتقدمة.
