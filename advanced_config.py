#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced configuration module for SmartPSS Pro
إعدادات متقدمة لنظام SmartPSS Pro
"""

import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

class AdvancedConfig:
    """مدير الإعدادات المتقدمة"""
    
    def __init__(self, config_file: str = "smartpss_pro_config.json"):
        self.config_file = Path(config_file)
        self.config_data = {}
        self.load_config()
    
    def load_config(self):
        """تحميل الإعدادات من الملف"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
            else:
                self.config_data = self.get_default_config()
                self.save_config()
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            self.config_data = self.get_default_config()
    
    def save_config(self):
        """حفظ الإعدادات في الملف"""
        try:
            self.config_data['last_updated'] = datetime.now().isoformat()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def get_default_config(self) -> Dict[str, Any]:
        """الحصول على الإعدادات الافتراضية"""
        return {
            "version": "2.0.0",
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            
            # إعدادات التطبيق الأساسية
            "application": {
                "name": "SmartPSS Pro",
                "language": "ar",
                "theme": "dark",
                "auto_start": False,
                "minimize_to_tray": True,
                "check_updates": True,
                "send_analytics": False
            },
            
            # إعدادات الأداء المتقدمة
            "performance": {
                "max_concurrent_streams": 16,
                "video_buffer_size": 10,
                "memory_limit_mb": 2048,
                "cpu_usage_limit": 80,
                "gpu_acceleration": True,
                "multi_threading": True,
                "frame_skip_threshold": 5,
                "optimization_level": "balanced"
            },
            
            # إعدادات الفيديو المتقدمة
            "video": {
                "default_resolution": "1920x1080",
                "supported_codecs": ["h264", "h265", "mjpeg"],
                "hardware_decoding": True,
                "deinterlacing": True,
                "noise_reduction": False,
                "color_correction": False,
                "hdr_support": False
            },
            
            # إعدادات كشف الحركة بالذكاء الاصطناعي
            "ai_detection": {
                "enabled": False,
                "model_path": "models/",
                "confidence_threshold": 0.7,
                "nms_threshold": 0.4,
                "object_classes": ["person", "car", "truck", "bicycle"],
                "face_recognition": {
                    "enabled": False,
                    "database_path": "faces/",
                    "recognition_threshold": 0.8
                },
                "license_plate": {
                    "enabled": False,
                    "regions": ["saudi", "uae", "egypt"],
                    "confidence_threshold": 0.9
                }
            },
            
            # إعدادات التخزين السحابي
            "cloud_storage": {
                "enabled": False,
                "provider": "none",
                "auto_upload": False,
                "upload_schedule": "daily",
                "encryption": True,
                "compression": True,
                "retention_days": 30,
                "providers": {
                    "google_drive": {
                        "client_id": "",
                        "client_secret": "",
                        "folder_id": ""
                    },
                    "dropbox": {
                        "app_key": "",
                        "app_secret": "",
                        "access_token": ""
                    },
                    "aws_s3": {
                        "access_key": "",
                        "secret_key": "",
                        "bucket_name": "",
                        "region": "us-east-1"
                    }
                }
            },
            
            # إعدادات الأمان المتقدمة
            "security": {
                "encryption_enabled": True,
                "encryption_algorithm": "AES-256",
                "two_factor_auth": False,
                "session_timeout": 480,  # 8 hours
                "max_login_attempts": 5,
                "lockout_duration": 30,  # minutes
                "password_policy": {
                    "min_length": 8,
                    "require_uppercase": True,
                    "require_lowercase": True,
                    "require_numbers": True,
                    "require_symbols": False,
                    "expiry_days": 90
                },
                "audit_logging": True,
                "data_anonymization": False
            },
            
            # إعدادات الشبكة المتقدمة
            "network": {
                "connection_timeout": 10,
                "read_timeout": 30,
                "max_retries": 3,
                "retry_delay": 2,
                "bandwidth_limit": 0,  # 0 = unlimited
                "proxy": {
                    "enabled": False,
                    "type": "http",
                    "host": "",
                    "port": 8080,
                    "username": "",
                    "password": ""
                },
                "ssl_verification": True,
                "custom_certificates": []
            },
            
            # إعدادات التنبيهات المتقدمة
            "alerts": {
                "sound_enabled": True,
                "sound_volume": 80,
                "email_enabled": False,
                "sms_enabled": False,
                "push_notifications": False,
                "webhook_enabled": False,
                "alert_cooldown": 60,  # seconds
                "severity_levels": {
                    "low": {"color": "#FFC107", "sound": "beep.wav"},
                    "medium": {"color": "#FF9800", "sound": "alert.wav"},
                    "high": {"color": "#F44336", "sound": "alarm.wav"},
                    "critical": {"color": "#D32F2F", "sound": "emergency.wav"}
                }
            },
            
            # إعدادات النسخ الاحتياطي
            "backup": {
                "enabled": True,
                "schedule": "daily",
                "time": "02:00",
                "location": "backups/",
                "retention_days": 7,
                "compression": True,
                "encryption": True,
                "include_videos": False,
                "include_logs": True,
                "include_settings": True,
                "cloud_backup": False
            },
            
            # إعدادات التطوير والتشخيص
            "development": {
                "debug_mode": False,
                "verbose_logging": False,
                "performance_profiling": False,
                "memory_profiling": False,
                "crash_dumps": True,
                "telemetry": False,
                "beta_features": False
            },
            
            # إعدادات التكامل مع الأنظمة الخارجية
            "integrations": {
                "home_assistant": {
                    "enabled": False,
                    "url": "",
                    "token": ""
                },
                "mqtt": {
                    "enabled": False,
                    "broker": "localhost",
                    "port": 1883,
                    "username": "",
                    "password": "",
                    "topic_prefix": "smartpss"
                },
                "webhook": {
                    "enabled": False,
                    "url": "",
                    "secret": "",
                    "events": ["motion", "recording", "alert"]
                }
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """الحصول على قيمة إعداد"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """تعيين قيمة إعداد"""
        keys = key.split('.')
        config = self.config_data
        
        try:
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            config[keys[-1]] = value
            return self.save_config()
        except Exception as e:
            print(f"خطأ في تعيين الإعداد {key}: {e}")
            return False
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات للقيم الافتراضية"""
        self.config_data = self.get_default_config()
        return self.save_config()
    
    def export_config(self, file_path: str) -> bool:
        """تصدير الإعدادات لملف"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"خطأ في تصدير الإعدادات: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """استيراد الإعدادات من ملف"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            
            # دمج الإعدادات المستوردة مع الحالية
            self.config_data.update(imported_config)
            return self.save_config()
        except Exception as e:
            print(f"خطأ في استيراد الإعدادات: {e}")
            return False
    
    def validate_config(self) -> Dict[str, list]:
        """التحقق من صحة الإعدادات"""
        errors = []
        warnings = []
        
        # فحص الإعدادات الأساسية
        if not self.get('application.name'):
            errors.append("اسم التطبيق مفقود")
        
        if self.get('performance.memory_limit_mb', 0) < 512:
            warnings.append("حد الذاكرة منخفض جداً")
        
        if self.get('security.session_timeout', 0) < 60:
            warnings.append("مهلة الجلسة قصيرة جداً")
        
        return {"errors": errors, "warnings": warnings}

# إنشاء مثيل عام للإعدادات
advanced_config = AdvancedConfig()
