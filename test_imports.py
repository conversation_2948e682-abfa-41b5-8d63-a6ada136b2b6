#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test imports for SmartPSS Pro
اختبار الاستيرادات
"""

print("🧪 اختبار الاستيرادات...")

try:
    print("1. اختبار PyQt5 الأساسي...")
    from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget
    print("✅ PyQt5 الأساسي يعمل")
    
    print("2. اختبار QComboBox...")
    from PyQt5.QtWidgets import QComboBox
    print("✅ QComboBox يعمل")
    
    print("3. اختبار جميع الاستيرادات...")
    from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                               QGridLayout, QPushButton, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,
                               QMenuB<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>usBar, Q<PERSON>oolBar,
                               QScrollArea, QGroupBox, QListWidget, QTabWidget,
                               QMessageBox, QDialog, QApplication, QComboBox)
    print("✅ جميع الاستيرادات تعمل")
    
    print("4. اختبار إنشاء QComboBox...")
    app = QApplication([])
    combo = QComboBox()
    combo.addItems(["1x1", "2x2", "3x3"])
    print("✅ إنشاء QComboBox يعمل")
    
    print("🎉 جميع الاختبارات نجحت!")
    
except Exception as e:
    print(f"❌ خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()
