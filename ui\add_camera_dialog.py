#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add Camera Dialog for SmartPSS Pro
نافذة إضافة الكاميرا
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                           QLineEdit, QComboBox, QSpinBox, QPushButton, 
                           QLabel, QGroupBox, QCheckBox, QTabWidget,
                           QWidget, QMessageBox, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont
import cv2
import threading

class CameraTestThread(QThread):
    """خيط اختبار الكاميرا"""
    test_result = pyqtSignal(bool, str)
    
    def __init__(self, camera_url):
        super().__init__()
        self.camera_url = camera_url
    
    def run(self):
        """اختبار الاتصال بالكاميرا"""
        try:
            cap = cv2.VideoCapture(self.camera_url)
            if cap.isOpened():
                ret, frame = cap.read()
                cap.release()
                if ret:
                    self.test_result.emit(True, "تم الاتصال بالكاميرا بنجاح!")
                else:
                    self.test_result.emit(False, "فشل في قراءة الإطار من الكاميرا")
            else:
                self.test_result.emit(False, "فشل في الاتصال بالكاميرا")
        except Exception as e:
            self.test_result.emit(False, f"خطأ في الاتصال: {str(e)}")

class AddCameraDialog(QDialog):
    """نافذة إضافة كاميرا جديدة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إضافة كاميرا جديدة")
        self.setModal(True)
        self.resize(500, 400)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # تبويبات
        tabs = QTabWidget()
        
        # تبويب الإعدادات الأساسية
        basic_tab = self.create_basic_tab()
        tabs.addTab(basic_tab, "الإعدادات الأساسية")
        
        # تبويب الإعدادات المتقدمة
        advanced_tab = self.create_advanced_tab()
        tabs.addTab(advanced_tab, "الإعدادات المتقدمة")
        
        layout.addWidget(tabs)
        
        # شريط التقدم للاختبار
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # أزرار التحكم
        button_layout = QHBoxLayout()
        
        self.test_button = QPushButton("اختبار الاتصال")
        self.test_button.clicked.connect(self.test_camera)
        
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.save_camera)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.test_button)
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
        
        # تطبيق النمط
        self.apply_style()
    
    def create_basic_tab(self):
        """إنشاء تبويب الإعدادات الأساسية"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # معلومات الكاميرا
        info_group = QGroupBox("معلومات الكاميرا")
        info_layout = QFormLayout()
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("مثال: كاميرا المدخل الرئيسي")
        info_layout.addRow("اسم الكاميرا:", self.name_edit)
        
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("وصف اختياري للكاميرا")
        info_layout.addRow("الوصف:", self.description_edit)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # إعدادات الاتصال
        connection_group = QGroupBox("إعدادات الاتصال")
        connection_layout = QFormLayout()
        
        self.type_combo = QComboBox()
        self.type_combo.addItems(["RTSP", "HTTP", "USB Camera", "IP Camera"])
        self.type_combo.currentTextChanged.connect(self.on_type_changed)
        connection_layout.addRow("نوع الكاميرا:", self.type_combo)
        
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("rtsp://*************:554/stream")
        connection_layout.addRow("عنوان URL:", self.url_edit)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("admin")
        connection_layout.addRow("اسم المستخدم:", self.username_edit)
        
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("كلمة المرور")
        connection_layout.addRow("كلمة المرور:", self.password_edit)
        
        connection_group.setLayout(connection_layout)
        layout.addWidget(connection_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        return tab
    
    def create_advanced_tab(self):
        """إنشاء تبويب الإعدادات المتقدمة"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات الفيديو
        video_group = QGroupBox("إعدادات الفيديو")
        video_layout = QFormLayout()
        
        self.resolution_combo = QComboBox()
        self.resolution_combo.addItems(["1920x1080", "1280x720", "640x480", "320x240"])
        video_layout.addRow("الدقة:", self.resolution_combo)
        
        self.fps_spin = QSpinBox()
        self.fps_spin.setRange(1, 60)
        self.fps_spin.setValue(25)
        video_layout.addRow("معدل الإطارات:", self.fps_spin)
        
        video_group.setLayout(video_layout)
        layout.addWidget(video_group)
        
        # إعدادات التسجيل
        recording_group = QGroupBox("إعدادات التسجيل")
        recording_layout = QVBoxLayout()
        
        self.auto_record_check = QCheckBox("تسجيل تلقائي")
        self.motion_detect_check = QCheckBox("كشف الحركة")
        self.motion_detect_check.setChecked(True)
        
        recording_layout.addWidget(self.auto_record_check)
        recording_layout.addWidget(self.motion_detect_check)
        
        recording_group.setLayout(recording_layout)
        layout.addWidget(recording_group)
        
        # إعدادات التنبيهات
        alerts_group = QGroupBox("إعدادات التنبيهات")
        alerts_layout = QVBoxLayout()
        
        self.email_alert_check = QCheckBox("تنبيه بالبريد الإلكتروني")
        self.sound_alert_check = QCheckBox("تنبيه صوتي")
        self.sound_alert_check.setChecked(True)
        
        alerts_layout.addWidget(self.email_alert_check)
        alerts_layout.addWidget(self.sound_alert_check)
        
        alerts_group.setLayout(alerts_layout)
        layout.addWidget(alerts_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        return tab
    
    def on_type_changed(self, camera_type):
        """تغيير نوع الكاميرا"""
        if camera_type == "USB Camera":
            self.url_edit.setPlaceholderText("0")
            self.url_edit.setText("0")
        elif camera_type == "RTSP":
            self.url_edit.setPlaceholderText("rtsp://*************:554/stream")
        elif camera_type == "HTTP":
            self.url_edit.setPlaceholderText("http://*************:8080/video")
        elif camera_type == "IP Camera":
            self.url_edit.setPlaceholderText("*************")
    
    def test_camera(self):
        """اختبار الاتصال بالكاميرا"""
        if not self.url_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال عنوان الكاميرا")
            return
        
        self.test_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم لا نهائي
        
        # بناء URL الكاميرا
        camera_url = self.build_camera_url()
        
        # بدء اختبار الكاميرا
        self.test_thread = CameraTestThread(camera_url)
        self.test_thread.test_result.connect(self.on_test_result)
        self.test_thread.start()
    
    def build_camera_url(self):
        """بناء URL الكاميرا"""
        camera_type = self.type_combo.currentText()
        url = self.url_edit.text().strip()
        username = self.username_edit.text().strip()
        password = self.password_edit.text().strip()
        
        if camera_type == "USB Camera":
            try:
                return int(url)
            except:
                return 0
        elif camera_type == "RTSP" and username and password:
            # إضافة المصادقة للـ RTSP
            if "://" in url:
                protocol, rest = url.split("://", 1)
                return f"{protocol}://{username}:{password}@{rest}"
            else:
                return f"rtsp://{username}:{password}@{url}"
        else:
            return url
    
    def on_test_result(self, success, message):
        """معالجة نتيجة اختبار الكاميرا"""
        self.test_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            QMessageBox.information(self, "نجح الاختبار", message)
        else:
            QMessageBox.warning(self, "فشل الاختبار", message)
    
    def save_camera(self):
        """حفظ إعدادات الكاميرا"""
        # التحقق من البيانات المطلوبة
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الكاميرا")
            return
        
        if not self.url_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال عنوان الكاميرا")
            return
        
        # جمع البيانات
        camera_data = {
            'name': self.name_edit.text().strip(),
            'description': self.description_edit.text().strip(),
            'type': self.type_combo.currentText(),
            'url': self.build_camera_url(),
            'username': self.username_edit.text().strip(),
            'password': self.password_edit.text().strip(),
            'resolution': self.resolution_combo.currentText(),
            'fps': self.fps_spin.value(),
            'auto_record': self.auto_record_check.isChecked(),
            'motion_detect': self.motion_detect_check.isChecked(),
            'email_alert': self.email_alert_check.isChecked(),
            'sound_alert': self.sound_alert_check.isChecked()
        }

        print(f"🔍 بيانات الكاميرا المجمعة: {camera_data}")
        
        # حفظ البيانات (سيتم ربطها بقاعدة البيانات)
        self.camera_data = camera_data
        
        QMessageBox.information(self, "تم الحفظ", "تم حفظ إعدادات الكاميرا بنجاح!")
        self.accept()
    
    def get_camera_data(self):
        """الحصول على بيانات الكاميرا"""
        return getattr(self, 'camera_data', None)
    
    def apply_style(self):
        """تطبيق النمط"""
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: white;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 8px;
                margin: 10px 0;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QLineEdit, QComboBox, QSpinBox {
                background-color: #3b3b3b;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 8px;
                color: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus {
                border: 2px solid #2196F3;
            }
            QPushButton {
                background-color: #2196F3;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                color: white;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
            QPushButton:disabled {
                background-color: #666;
                color: #999;
            }
            QCheckBox {
                color: white;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #555;
                background-color: #3b3b3b;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #2196F3;
                background-color: #2196F3;
                border-radius: 3px;
            }
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #2b2b2b;
            }
            QTabBar::tab {
                background-color: #3b3b3b;
                color: white;
                padding: 8px 16px;
                margin: 2px;
                border-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
            }
            QProgressBar {
                border: 1px solid #555;
                border-radius: 4px;
                text-align: center;
                background-color: #3b3b3b;
                color: white;
            }
            QProgressBar::chunk {
                background-color: #2196F3;
                border-radius: 3px;
            }
        """)
